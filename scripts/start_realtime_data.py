#!/usr/bin/env python3
"""
Real-time data collection script for production deployment.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import argparse
from datetime import datetime, timed<PERSON>ta
from loguru import logger

from crypto_markov_trader.data_collection import HyperliquidClient, Twitter<PERSON>lient
from crypto_markov_trader.feature_engineering import FeaturePipeline, SentimentA<PERSON>yzer
from crypto_markov_trader.utils import setup_logging


class RealTimeDataCollector:
    """Real-time data collection for production trading."""
    
    def __init__(self, symbols: list, update_interval: int = 60):
        """
        Initialize real-time data collector.
        
        Args:
            symbols: List of symbols to collect data for
            update_interval: Update interval in seconds
        """
        self.symbols = symbols
        self.update_interval = update_interval
        self.running = False
        
        # Initialize clients
        self.hyperliquid = HyperliquidClient()
        self.twitter = TwitterClient()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.feature_pipeline = FeaturePipeline()
        
        logger.info(f"Real-time data collector initialized for {symbols}")
    
    async def start_collection(self):
        """Start real-time data collection."""
        try:
            self.running = True
            logger.info("🚀 Starting real-time data collection...")
            
            # Start collection tasks
            tasks = [
                self._market_data_loop(),
                self._sentiment_data_loop(),
                self._feature_update_loop()
            ]
            
            await asyncio.gather(*tasks)
            
        except KeyboardInterrupt:
            logger.info("Stopping real-time data collection...")
            self.running = False
        except Exception as e:
            logger.error(f"Error in real-time data collection: {e}")
            self.running = False
    
    async def _market_data_loop(self):
        """Continuous market data collection loop."""
        while self.running:
            try:
                for symbol in self.symbols:
                    await self._collect_market_data(symbol)
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in market data loop: {e}")
                await asyncio.sleep(10)
    
    async def _sentiment_data_loop(self):
        """Continuous sentiment data collection loop."""
        while self.running:
            try:
                for symbol in self.symbols:
                    await self._collect_sentiment_data(symbol)
                
                # Update sentiment less frequently (every 5 minutes)
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"Error in sentiment data loop: {e}")
                await asyncio.sleep(60)
    
    async def _feature_update_loop(self):
        """Continuous feature engineering loop."""
        while self.running:
            try:
                for symbol in self.symbols:
                    await self._update_features(symbol)
                
                # Update features every 2 minutes
                await asyncio.sleep(120)
                
            except Exception as e:
                logger.error(f"Error in feature update loop: {e}")
                await asyncio.sleep(30)
    
    async def _collect_market_data(self, symbol: str):
        """Collect latest market data for symbol."""
        try:
            # Get latest candles (last 100 for technical indicators)
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=2)
            
            market_data = self.hyperliquid.get_historical_data(
                symbol=symbol,
                interval="1m",
                start_time=start_time,
                end_time=end_time,
                limit=120
            )
            
            if not market_data.empty:
                # Save to real-time data store
                filename = f"data/realtime/{symbol}_market_data.parquet"
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                market_data.to_parquet(filename)
                
                logger.info(f"📊 Updated market data for {symbol}: {len(market_data)} candles")
            else:
                logger.warning(f"No market data received for {symbol}")
                
        except Exception as e:
            logger.error(f"Error collecting market data for {symbol}: {e}")
    
    async def _collect_sentiment_data(self, symbol: str):
        """Collect latest sentiment data for symbol."""
        try:
            # Get recent tweets
            tweets = self.twitter.search_tweets(
                query=f"{symbol} OR ${symbol}",
                max_results=100,
                hours_back=1
            )
            
            if tweets:
                # Analyze sentiment
                sentiment_data = self.sentiment_analyzer.analyze_tweets(tweets)
                
                # Save sentiment data
                filename = f"data/realtime/{symbol}_sentiment_data.json"
                os.makedirs(os.path.dirname(filename), exist_ok=True)
                
                import json
                with open(filename, 'w') as f:
                    json.dump({
                        'timestamp': datetime.now().isoformat(),
                        'symbol': symbol,
                        'sentiment_data': sentiment_data,
                        'tweet_count': len(tweets)
                    }, f, indent=2)
                
                logger.info(f"🐦 Updated sentiment data for {symbol}: {len(tweets)} tweets")
            else:
                logger.warning(f"No tweets found for {symbol}")
                
        except Exception as e:
            logger.error(f"Error collecting sentiment data for {symbol}: {e}")
    
    async def _update_features(self, symbol: str):
        """Update features for symbol."""
        try:
            # Load latest market data
            market_file = f"data/realtime/{symbol}_market_data.parquet"
            if not os.path.exists(market_file):
                return
            
            market_data = pd.read_parquet(market_file)
            
            # Create features
            features_df = self.feature_pipeline.create_features(market_data, symbol)
            
            if not features_df.empty:
                # Save features
                features_file = f"data/realtime/{symbol}_features.parquet"
                features_df.to_parquet(features_file)
                
                logger.info(f"⚙️ Updated features for {symbol}: {len(features_df.columns)} features")
            
        except Exception as e:
            logger.error(f"Error updating features for {symbol}: {e}")
    
    def stop_collection(self):
        """Stop data collection."""
        self.running = False
        logger.info("Data collection stopped")


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Real-time data collection")
    parser.add_argument("--symbols", default="BTC,ETH,SOL", help="Comma-separated symbols")
    parser.add_argument("--interval", type=int, default=60, help="Update interval in seconds")
    parser.add_argument("--log-level", default="INFO", help="Log level")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(level=args.log_level)
    
    # Parse symbols
    symbols = [s.strip() for s in args.symbols.split(',')]
    
    # Create collector
    collector = RealTimeDataCollector(symbols, args.interval)
    
    try:
        # Start collection
        await collector.start_collection()
    except KeyboardInterrupt:
        logger.info("Shutting down real-time data collection...")
        collector.stop_collection()


if __name__ == "__main__":
    import pandas as pd
    asyncio.run(main())
