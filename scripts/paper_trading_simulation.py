#!/usr/bin/env python3
"""
Paper trading simulation using the complete bot system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import argparse
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger

from crypto_markov_trader.trading import LiveTrader, TradingMonitor
from crypto_markov_trader.utils import setup_logging


class PaperTradingSimulation:
    """Paper trading simulation with realistic market conditions."""
    
    def __init__(
        self,
        symbols: list,
        initial_capital: float = 10000,
        duration_hours: int = 24,
        verbose: bool = True
    ):
        """
        Initialize paper trading simulation.
        
        Args:
            symbols: List of symbols to trade
            initial_capital: Starting capital
            duration_hours: Simulation duration in hours
            verbose: Enable verbose logging
        """
        self.symbols = symbols
        self.initial_capital = initial_capital
        self.duration_hours = duration_hours
        self.verbose = verbose
        
        # Initialize trading system
        self.live_trader = LiveTrader(
            symbols=symbols,
            initial_capital=initial_capital
        )
        
        # Initialize monitoring
        self.monitor = TradingMonitor(self.live_trader)
        
        # Simulation state
        self.start_time = None
        self.end_time = None
        self.simulation_results = {}
        
        logger.info(f"📊 Paper Trading Simulation initialized")
        logger.info(f"   Symbols: {symbols}")
        logger.info(f"   Initial Capital: ${initial_capital:,.2f}")
        logger.info(f"   Duration: {duration_hours} hours")
    
    async def run_simulation(self):
        """Run the complete paper trading simulation."""
        try:
            self.start_time = datetime.now()
            self.end_time = self.start_time + timedelta(hours=self.duration_hours)
            
            logger.info("🚀 Starting Paper Trading Simulation")
            logger.info("=" * 60)
            logger.info(f"Start Time: {self.start_time}")
            logger.info(f"End Time: {self.end_time}")
            logger.info(f"Duration: {self.duration_hours} hours")
            
            # Display initial portfolio state
            await self._display_portfolio_status("INITIAL")
            
            # Start monitoring
            monitoring_task = asyncio.create_task(self.monitor.start_monitoring())
            
            # Run simulation loop
            simulation_task = asyncio.create_task(self._simulation_loop())
            
            # Wait for simulation to complete
            await simulation_task
            
            # Stop monitoring
            await self.monitor.stop_monitoring()
            
            # Generate final report
            await self._generate_final_report()
            
        except KeyboardInterrupt:
            logger.info("\n🛑 Simulation interrupted by user")
            await self._generate_final_report()
        except Exception as e:
            logger.error(f"❌ Simulation error: {e}")
            import traceback
            traceback.print_exc()
    
    async def _simulation_loop(self):
        """Main simulation loop."""
        iteration = 0
        
        while datetime.now() < self.end_time:
            try:
                iteration += 1
                current_time = datetime.now()
                elapsed_hours = (current_time - self.start_time).total_seconds() / 3600
                
                if self.verbose:
                    logger.info(f"📈 Simulation Step {iteration} - Elapsed: {elapsed_hours:.1f}h")
                
                # Update market data for all symbols
                await self._update_market_data()
                
                # Process trading signals
                await self._process_trading_signals()
                
                # Update portfolio
                await self._update_portfolio()
                
                # Display status every 30 minutes
                if iteration % 30 == 0:
                    await self._display_portfolio_status(f"STEP_{iteration}")
                
                # Display brief status every 5 minutes
                elif iteration % 5 == 0 and self.verbose:
                    await self._display_brief_status()
                
                # Sleep for 1 minute (simulation time)
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Error in simulation step {iteration}: {e}")
                await asyncio.sleep(10)
    
    async def _update_market_data(self):
        """Update market data for all symbols."""
        try:
            if self.verbose:
                logger.info("📊 Updating market data...")
            
            await self.live_trader._update_market_data()
            
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
    
    async def _process_trading_signals(self):
        """Process trading signals for all symbols."""
        try:
            if not self.live_trader.trading_enabled or self.live_trader.risk_manager.emergency_stop:
                return
            
            for symbol in self.symbols:
                if self.verbose:
                    logger.info(f"🔍 Processing signals for {symbol}...")
                
                await self.live_trader._process_symbol_signals(symbol)
                
        except Exception as e:
            logger.error(f"Error processing trading signals: {e}")
    
    async def _update_portfolio(self):
        """Update portfolio state."""
        try:
            await self.live_trader._update_portfolio()
            
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
    
    async def _display_portfolio_status(self, stage: str):
        """Display detailed portfolio status."""
        try:
            status = self.live_trader.get_status()
            summary = self.live_trader.portfolio_manager.get_portfolio_summary()
            
            logger.info(f"\n📊 PORTFOLIO STATUS - {stage}")
            logger.info("=" * 50)
            logger.info(f"Portfolio Value: ${status['portfolio_value']:,.2f}")
            logger.info(f"Cash Balance: ${summary['cash_balance']:,.2f}")
            logger.info(f"Positions Value: ${summary['positions_value']:,.2f}")
            logger.info(f"Daily P&L: ${status['daily_pnl']:+,.2f}")
            logger.info(f"Total Return: {summary['total_return']:+.2%}")
            logger.info(f"Max Drawdown: {summary['max_drawdown']:.2%}")
            logger.info(f"Risk Score: {status['risk_score']:.1f}/100")
            logger.info(f"Open Positions: {status['num_positions']}")
            logger.info(f"Trades Executed: {status['trading_stats']['executed_trades']}")
            logger.info(f"Trades Rejected: {status['trading_stats']['rejected_trades']}")
            
            # Show individual positions
            positions = self.live_trader.portfolio_manager.get_all_positions()
            if positions:
                logger.info("\n💼 Current Positions:")
                for symbol, position in positions.items():
                    logger.info(f"  {symbol}: {position.quantity:+.6f} @ ${position.average_price:.2f}")
                    logger.info(f"    Market Value: ${position.market_value:,.2f}")
                    logger.info(f"    Unrealized P&L: ${position.unrealized_pnl:+,.2f}")
            else:
                logger.info("\n💼 No open positions")
            
            logger.info("=" * 50)
            
        except Exception as e:
            logger.error(f"Error displaying portfolio status: {e}")
    
    async def _display_brief_status(self):
        """Display brief status update."""
        try:
            status = self.live_trader.get_status()
            elapsed_hours = (datetime.now() - self.start_time).total_seconds() / 3600
            
            logger.info(f"⏱️  [{elapsed_hours:.1f}h] Value: ${status['portfolio_value']:,.2f} | "
                       f"P&L: ${status['daily_pnl']:+,.2f} | "
                       f"Positions: {status['num_positions']} | "
                       f"Risk: {status['risk_score']:.0f}/100")
            
        except Exception as e:
            logger.error(f"Error displaying brief status: {e}")
    
    async def _generate_final_report(self):
        """Generate comprehensive final simulation report."""
        try:
            end_time = datetime.now()
            duration = end_time - self.start_time
            
            status = self.live_trader.get_status()
            summary = self.live_trader.portfolio_manager.get_portfolio_summary()
            risk_report = self.live_trader.risk_manager.get_risk_report()
            execution_stats = self.live_trader.execution_engine.get_execution_stats()
            
            logger.info("\n" + "=" * 80)
            logger.info("🎯 PAPER TRADING SIMULATION - FINAL REPORT")
            logger.info("=" * 80)
            
            # Simulation Overview
            logger.info(f"📅 Simulation Period:")
            logger.info(f"   Start Time: {self.start_time}")
            logger.info(f"   End Time: {end_time}")
            logger.info(f"   Duration: {duration}")
            logger.info(f"   Symbols Traded: {', '.join(self.symbols)}")
            
            # Performance Summary
            logger.info(f"\n💰 Performance Summary:")
            logger.info(f"   Initial Capital: ${self.initial_capital:,.2f}")
            logger.info(f"   Final Value: ${status['portfolio_value']:,.2f}")
            logger.info(f"   Total P&L: ${status['portfolio_value'] - self.initial_capital:+,.2f}")
            logger.info(f"   Total Return: {summary['total_return']:+.2%}")
            logger.info(f"   Max Drawdown: {summary['max_drawdown']:.2%}")
            logger.info(f"   Sharpe Ratio: {summary.get('sharpe_ratio', 'N/A')}")
            
            # Trading Activity
            logger.info(f"\n📈 Trading Activity:")
            logger.info(f"   Total Signals: {status['trading_stats']['total_signals']}")
            logger.info(f"   Trades Executed: {status['trading_stats']['executed_trades']}")
            logger.info(f"   Trades Rejected: {status['trading_stats']['rejected_trades']}")
            logger.info(f"   Success Rate: {(status['trading_stats']['executed_trades'] / max(status['trading_stats']['total_signals'], 1)):.1%}")
            logger.info(f"   Total Volume: ${execution_stats['total_volume']:,.2f}")
            logger.info(f"   Total Fees: ${execution_stats['total_fees']:,.2f}")
            
            # Risk Analysis
            logger.info(f"\n⚠️  Risk Analysis:")
            logger.info(f"   Final Risk Score: {status['risk_score']:.1f}/100")
            logger.info(f"   Emergency Stops: {'Yes' if risk_report['emergency_stop'] else 'No'}")
            logger.info(f"   Risk Violations: {len(risk_report.get('recent_alerts', []))}")
            logger.info(f"   Max Leverage Used: {summary.get('leverage', 0):.1f}x")
            
            # Final Positions
            positions = self.live_trader.portfolio_manager.get_all_positions()
            if positions:
                logger.info(f"\n💼 Final Positions:")
                for symbol, position in positions.items():
                    logger.info(f"   {symbol}: {position.quantity:+.6f} @ ${position.average_price:.2f}")
                    logger.info(f"     Market Value: ${position.market_value:,.2f}")
                    logger.info(f"     Unrealized P&L: ${position.unrealized_pnl:+,.2f}")
            else:
                logger.info(f"\n💼 No final positions (all closed)")
            
            # Performance Rating
            total_return_pct = summary['total_return'] * 100
            if total_return_pct > 5:
                rating = "🌟 EXCELLENT"
            elif total_return_pct > 2:
                rating = "✅ GOOD"
            elif total_return_pct > 0:
                rating = "👍 POSITIVE"
            elif total_return_pct > -2:
                rating = "⚠️  SLIGHT LOSS"
            else:
                rating = "❌ POOR"
            
            logger.info(f"\n🏆 Performance Rating: {rating}")
            logger.info(f"📊 Return: {total_return_pct:+.2f}%")
            
            # Save detailed report
            report_data = {
                'simulation_config': {
                    'symbols': self.symbols,
                    'initial_capital': self.initial_capital,
                    'duration_hours': self.duration_hours,
                    'start_time': self.start_time.isoformat(),
                    'end_time': end_time.isoformat()
                },
                'performance': summary,
                'trading_stats': status['trading_stats'],
                'execution_stats': execution_stats,
                'risk_report': risk_report,
                'final_positions': [
                    {
                        'symbol': symbol,
                        'quantity': pos.quantity,
                        'average_price': pos.average_price,
                        'market_value': pos.market_value,
                        'unrealized_pnl': pos.unrealized_pnl
                    }
                    for symbol, pos in positions.items()
                ]
            }
            
            # Save report to file
            report_file = f"logs/paper_trading_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            os.makedirs(os.path.dirname(report_file), exist_ok=True)
            
            import json
            with open(report_file, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            logger.info(f"\n📄 Detailed report saved to: {report_file}")
            logger.info("=" * 80)
            
            return report_data
            
        except Exception as e:
            logger.error(f"Error generating final report: {e}")
            return {}


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Paper Trading Simulation")
    parser.add_argument("--symbols", default="BTC,ETH", help="Comma-separated symbols")
    parser.add_argument("--capital", type=float, default=10000, help="Initial capital")
    parser.add_argument("--hours", type=float, default=4, help="Simulation duration in hours")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")
    parser.add_argument("--log-level", default="INFO", help="Log level")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    # Parse symbols
    symbols = [s.strip() for s in args.symbols.split(',')]
    
    # Create and run simulation
    simulation = PaperTradingSimulation(
        symbols=symbols,
        initial_capital=args.capital,
        duration_hours=args.hours,
        verbose=args.verbose
    )
    
    await simulation.run_simulation()


if __name__ == "__main__":
    asyncio.run(main())
