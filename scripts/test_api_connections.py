#!/usr/bin/env python3
"""
Test API connections for production deployment.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
from datetime import datetime, timed<PERSON>ta
from loguru import logger

from crypto_markov_trader.data_collection import HyperliquidClient, TwitterClient
from crypto_markov_trader.feature_engineering import SentimentAnalyzer
from crypto_markov_trader.utils import setup_logging


class APIConnectionTester:
    """Test all API connections for production readiness."""
    
    def __init__(self):
        self.results = {}
        
    async def test_all_connections(self):
        """Test all API connections."""
        logger.info("🔍 Testing API connections...")
        
        tests = [
            ("Hyperliquid Market Data", self.test_hyperliquid_data),
            ("Twitter API", self.test_twitter_api),
            ("Sentiment Analysis", self.test_sentiment_analysis),
        ]
        
        for test_name, test_func in tests:
            try:
                logger.info(f"Testing {test_name}...")
                result = await test_func()
                self.results[test_name] = result
                
                if result['success']:
                    logger.info(f"✅ {test_name}: {result['message']}")
                else:
                    logger.error(f"❌ {test_name}: {result['message']}")
                    
            except Exception as e:
                logger.error(f"❌ {test_name}: Exception - {e}")
                self.results[test_name] = {'success': False, 'message': str(e)}
        
        return self.results
    
    async def test_hyperliquid_data(self):
        """Test Hyperliquid market data API."""
        try:
            client = HyperliquidClient()
            
            # Test data retrieval
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            data = client.get_historical_data(
                symbol="BTC",
                interval="1m",
                start_time=start_time,
                end_time=end_time,
                limit=60
            )
            
            if data.empty:
                return {'success': False, 'message': 'No data returned'}
            
            return {
                'success': True,
                'message': f'Retrieved {len(data)} candles, latest price: ${data["close"].iloc[-1]:,.2f}'
            }
            
        except Exception as e:
            return {'success': False, 'message': f'Connection failed: {e}'}
    
    async def test_twitter_api(self):
        """Test Twitter API connection."""
        try:
            client = TwitterClient()
            
            # Test tweet search
            tweets = client.search_tweets(
                query="BTC OR bitcoin",
                max_results=10
            )
            
            if not tweets:
                return {'success': False, 'message': 'No tweets returned'}
            
            return {
                'success': True,
                'message': f'Retrieved {len(tweets)} tweets'
            }
            
        except Exception as e:
            return {'success': False, 'message': f'Twitter API failed: {e}'}
    
    async def test_sentiment_analysis(self):
        """Test sentiment analysis model."""
        try:
            analyzer = SentimentAnalyzer()
            
            # Test with sample text
            test_texts = [
                "Bitcoin is going to the moon! 🚀",
                "Crypto market is crashing badly",
                "BTC holding steady at support levels"
            ]
            
            results = []
            for text in test_texts:
                result = analyzer.analyze_text(text)
                results.append(result)
            
            if not results:
                return {'success': False, 'message': 'No sentiment results'}
            
            return {
                'success': True,
                'message': f'Analyzed {len(results)} texts successfully'
            }
            
        except Exception as e:
            return {'success': False, 'message': f'Sentiment analysis failed: {e}'}
    
    def generate_report(self):
        """Generate connection test report."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'PASS' if all(r['success'] for r in self.results.values()) else 'FAIL',
            'test_results': self.results
        }
        
        # Save report
        report_file = f"logs/api_connection_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        os.makedirs(os.path.dirname(report_file), exist_ok=True)
        
        import json
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        logger.info(f"Connection test report saved to: {report_file}")
        return report


async def main():
    """Main function."""
    setup_logging()
    
    tester = APIConnectionTester()
    
    print("🔍 Testing API Connections for Production Deployment")
    print("=" * 60)
    
    # Run tests
    results = await tester.test_all_connections()
    
    # Generate report
    report = tester.generate_report()
    
    # Summary
    print("\n📊 Connection Test Summary:")
    print("-" * 30)
    
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    print(f"Tests Passed: {passed}/{total}")
    print(f"Overall Status: {report['overall_status']}")
    
    if report['overall_status'] == 'PASS':
        print("\n✅ All API connections are working correctly!")
        print("🚀 System is ready for production deployment.")
    else:
        print("\n❌ Some API connections failed.")
        print("⚠️  Please fix the issues before deploying to production.")
        
        print("\nFailed Tests:")
        for test_name, result in results.items():
            if not result['success']:
                print(f"  • {test_name}: {result['message']}")
    
    return report['overall_status'] == 'PASS'


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
