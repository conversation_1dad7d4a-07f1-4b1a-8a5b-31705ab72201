#!/usr/bin/env python3
"""
Emergency stop script for immediate trading halt.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import asyncio
import argparse
from datetime import datetime
from loguru import logger

from crypto_markov_trader.trading import LiveTrader
from crypto_markov_trader.utils import setup_logging


async def emergency_stop_all():
    """Execute emergency stop for all trading activities."""
    try:
        logger.critical("🚨 EMERGENCY STOP INITIATED 🚨")
        
        # Initialize trader (this will connect to existing instances)
        trader = LiveTrader()
        
        # Execute emergency stop
        await trader.emergency_stop()
        
        # Cancel all active orders
        cancelled_orders = await trader.execution_engine.emergency_cancel_all()
        logger.critical(f"Cancelled {cancelled_orders} active orders")
        
        # Generate emergency report
        status = trader.get_status()
        
        logger.critical("EMERGENCY STOP REPORT:")
        logger.critical(f"Timestamp: {datetime.now()}")
        logger.critical(f"Portfolio Value: ${status.get('portfolio_value', 0):,.2f}")
        logger.critical(f"Open Positions: {status.get('num_positions', 0)}")
        logger.critical(f"Daily P&L: ${status.get('daily_pnl', 0):+,.2f}")
        logger.critical(f"Risk Score: {status.get('risk_score', 0):.1f}/100")
        
        # Save emergency report
        report_file = f"logs/emergency_stop_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        import json
        with open(report_file, 'w') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'action': 'emergency_stop',
                'status': status,
                'cancelled_orders': cancelled_orders
            }, f, indent=2)
        
        logger.critical(f"Emergency report saved to: {report_file}")
        logger.critical("🛑 EMERGENCY STOP COMPLETED")
        
        return True
        
    except Exception as e:
        logger.error(f"Error during emergency stop: {e}")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Emergency stop all trading")
    parser.add_argument("--confirm", action="store_true", help="Confirm emergency stop")
    
    args = parser.parse_args()
    
    if not args.confirm:
        print("⚠️  EMERGENCY STOP")
        print("This will immediately halt all trading activities and cancel all orders.")
        confirm = input("Are you sure you want to proceed? (type 'EMERGENCY STOP' to confirm): ")
        
        if confirm != "EMERGENCY STOP":
            print("Emergency stop cancelled.")
            return
    
    # Setup logging
    setup_logging(level="CRITICAL")
    
    # Execute emergency stop
    success = asyncio.run(emergency_stop_all())
    
    if success:
        print("✅ Emergency stop completed successfully")
    else:
        print("❌ Emergency stop failed - check logs")


if __name__ == "__main__":
    main()
