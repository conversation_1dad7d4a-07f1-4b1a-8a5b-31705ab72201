"""
Tests for feature engineering modules.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_markov_trader.feature_engineering import TechnicalIndicators, SentimentAnalyzer, FeaturePipeline


@pytest.fixture
def sample_ohlcv_data():
    """Create sample OHLCV data for testing."""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    np.random.seed(42)
    
    # Generate realistic price data
    base_price = 50000
    returns = np.random.normal(0, 0.001, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.0005))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.0005))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, 100)
    })
    
    df.set_index('timestamp', inplace=True)
    return df


@pytest.fixture
def sample_tweets():
    """Create sample tweet data for testing."""
    tweets = [
        {
            'id': '1',
            'text': 'Bitcoin is going to the moon! 🚀 #BTC',
            'created_at': datetime.now() - timedelta(minutes=5),
            'followers_count': 1000,
            'like_count': 50,
            'retweet_count': 10
        },
        {
            'id': '2',
            'text': 'Crypto market is crashing, sell everything!',
            'created_at': datetime.now() - timedelta(minutes=3),
            'followers_count': 500,
            'like_count': 5,
            'retweet_count': 2
        },
        {
            'id': '3',
            'text': 'BTC price is stable today, waiting for next move',
            'created_at': datetime.now() - timedelta(minutes=1),
            'followers_count': 2000,
            'like_count': 20,
            'retweet_count': 5
        }
    ]
    return tweets


class TestTechnicalIndicators:
    """Test technical indicators calculation."""
    
    def test_calculate_all_indicators(self, sample_ohlcv_data):
        """Test that all technical indicators are calculated."""
        ti = TechnicalIndicators()
        result = ti.calculate_all_indicators(sample_ohlcv_data)
        
        # Check that original columns are preserved
        assert all(col in result.columns for col in sample_ohlcv_data.columns)
        
        # Check that some key indicators are added
        expected_indicators = ['ema_12', 'ema_26', 'rsi', 'macd', 'bb_upper', 'atr']
        for indicator in expected_indicators:
            assert indicator in result.columns, f"Missing indicator: {indicator}"
        
        # Check that no infinite values exist
        assert not np.isinf(result.select_dtypes(include=[np.number])).any().any()
        
        # Check that result has same length as input
        assert len(result) == len(sample_ohlcv_data)
    
    def test_empty_dataframe_handling(self):
        """Test handling of empty DataFrame."""
        ti = TechnicalIndicators()
        empty_df = pd.DataFrame()
        result = ti.calculate_all_indicators(empty_df)
        
        assert result.empty
    
    def test_missing_columns_handling(self):
        """Test handling of DataFrame with missing required columns."""
        ti = TechnicalIndicators()
        incomplete_df = pd.DataFrame({'close': [1, 2, 3, 4, 5]})
        result = ti.calculate_all_indicators(incomplete_df)
        
        # Should return original DataFrame when required columns are missing
        assert list(result.columns) == ['close']


class TestSentimentAnalyzer:
    """Test sentiment analysis functionality."""
    
    def test_analyze_text_basic(self):
        """Test basic text sentiment analysis."""
        analyzer = SentimentAnalyzer()
        
        # Test positive text
        positive_result = analyzer.analyze_text("Bitcoin is amazing! Great investment!")
        assert isinstance(positive_result, dict)
        assert all(key in positive_result for key in ['positive', 'negative', 'neutral', 'compound'])
        
        # Test negative text
        negative_result = analyzer.analyze_text("Crypto is crashing! Sell everything!")
        assert isinstance(negative_result, dict)
        
        # Test empty text
        empty_result = analyzer.analyze_text("")
        assert all(score == 0.0 for score in empty_result.values())
    
    def test_analyze_tweets(self, sample_tweets):
        """Test tweet sentiment analysis."""
        analyzer = SentimentAnalyzer()
        
        # Skip test if model is not loaded (e.g., in CI environment)
        if not analyzer.is_model_loaded():
            pytest.skip("Sentiment model not loaded")
        
        result = analyzer.analyze_tweets(sample_tweets)
        
        assert len(result) == len(sample_tweets)
        
        # Check that sentiment scores are added
        for tweet in result:
            assert 'sentiment_positive' in tweet
            assert 'sentiment_negative' in tweet
            assert 'sentiment_neutral' in tweet
            assert 'sentiment_compound' in tweet
            assert 'sentiment_label' in tweet
    
    def test_aggregate_sentiment_scores(self, sample_tweets):
        """Test sentiment score aggregation."""
        analyzer = SentimentAnalyzer()
        
        # Add mock sentiment scores
        for i, tweet in enumerate(sample_tweets):
            tweet.update({
                'sentiment_positive': 0.1 + i * 0.1,
                'sentiment_negative': 0.05,
                'sentiment_neutral': 0.85 - i * 0.1,
                'sentiment_compound': 0.05 + i * 0.1
            })
        
        result = analyzer.aggregate_sentiment_scores(sample_tweets)
        
        assert isinstance(result, dict)
        assert all(key in result for key in ['positive', 'negative', 'neutral', 'compound'])
        assert all(isinstance(score, float) for score in result.values())
    
    def test_create_sentiment_features(self, sample_tweets):
        """Test sentiment feature creation."""
        analyzer = SentimentAnalyzer()
        
        # Add mock sentiment scores
        for i, tweet in enumerate(sample_tweets):
            tweet.update({
                'sentiment_positive': 0.1 + i * 0.1,
                'sentiment_negative': 0.05,
                'sentiment_neutral': 0.85 - i * 0.1,
                'sentiment_compound': 0.05 + i * 0.1
            })
        
        result = analyzer.create_sentiment_features(sample_tweets, timeframe="1min")
        
        if not result.empty:
            assert isinstance(result.index, pd.DatetimeIndex)
            # Check that sentiment columns exist
            sentiment_cols = [col for col in result.columns if 'sentiment' in col]
            assert len(sentiment_cols) > 0


class TestFeaturePipeline:
    """Test the complete feature pipeline."""
    
    def test_create_features_market_only(self, sample_ohlcv_data):
        """Test feature creation with market data only."""
        pipeline = FeaturePipeline()
        result = pipeline.create_features(sample_ohlcv_data, tweets=None, symbol="BTC")
        
        # Should have more columns than original data
        assert len(result.columns) > len(sample_ohlcv_data.columns)
        
        # Should have same number of rows
        assert len(result) == len(sample_ohlcv_data)
        
        # Should contain technical indicators
        assert any('ema' in col for col in result.columns)
        assert any('rsi' in col for col in result.columns)
    
    def test_create_features_with_sentiment(self, sample_ohlcv_data, sample_tweets):
        """Test feature creation with both market data and sentiment."""
        pipeline = FeaturePipeline()
        
        # Skip if sentiment analyzer is not available
        if not pipeline.sentiment_analyzer.is_model_loaded():
            pytest.skip("Sentiment model not loaded")
        
        result = pipeline.create_features(
            sample_ohlcv_data, 
            tweets=sample_tweets, 
            symbol="BTC"
        )
        
        # Should have technical indicators
        assert any('ema' in col for col in result.columns)
        
        # May have sentiment features (depending on time alignment)
        # This is optional since time alignment might not work with sample data
    
    def test_get_model_ready_features(self, sample_ohlcv_data):
        """Test preparation of features for ML models."""
        pipeline = FeaturePipeline()
        features_df = pipeline.create_features(sample_ohlcv_data, symbol="BTC")
        
        X, y = pipeline.get_model_ready_features(features_df)
        
        # Should return numeric features only
        assert X.select_dtypes(include=[np.number]).shape == X.shape
        
        # Should exclude OHLCV columns
        ohlcv_cols = ['open', 'high', 'low', 'close', 'volume']
        assert not any(col in X.columns for col in ohlcv_cols)
        
        # y should be None when no target column specified
        assert y is None
    
    def test_feature_importance_groups(self):
        """Test feature importance grouping."""
        pipeline = FeaturePipeline()
        groups = pipeline.get_feature_importance_groups()
        
        assert isinstance(groups, dict)
        expected_groups = ['price_action', 'trend', 'momentum', 'volatility', 'volume', 'sentiment', 'regime', 'time']
        assert all(group in groups for group in expected_groups)
        
        # Each group should contain a list of feature names
        for group, features in groups.items():
            assert isinstance(features, list)
