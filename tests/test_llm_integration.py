"""
Tests for LLM integration components.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_markov_trader.llm_integration import LLMTradingAdvisor, PromptBuilder, ContextAnalyzer
from crypto_markov_trader.models import TradingSignal


@pytest.fixture
def sample_features_data():
    """Create sample features data for testing."""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=100, freq='1min')
    
    # Generate realistic feature data
    base_price = 50000
    returns = np.random.normal(0, 0.002, 100)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, 100),
        
        # Technical indicators
        'ema_12': np.random.uniform(49000, 51000, 100),
        'ema_26': np.random.uniform(49000, 51000, 100),
        'sma_20': np.random.uniform(49000, 51000, 100),
        'sma_50': np.random.uniform(49000, 51000, 100),
        'rsi': np.random.uniform(20, 80, 100),
        'macd': np.random.normal(0, 100, 100),
        'macd_signal': np.random.normal(0, 100, 100),
        'macd_histogram': np.random.normal(0, 50, 100),
        'bb_upper': [p * 1.02 for p in prices],
        'bb_lower': [p * 0.98 for p in prices],
        'atr': np.random.uniform(100, 500, 100),
        'volume_ratio': np.random.uniform(0.5, 2.0, 100),
        
        # Custom features
        'support_level': [p * 0.95 for p in prices],
        'resistance_level': [p * 1.05 for p in prices],
    })
    
    df.set_index('timestamp', inplace=True)
    return df


@pytest.fixture
def sample_regime_info():
    """Create sample regime information."""
    return {
        'regime': 'Bull Market',
        'confidence': 0.75,
        'probabilities': {
            'Bull Market': 0.75,
            'Bear Market': 0.15,
            'Sideways Market': 0.10
        }
    }


@pytest.fixture
def sample_sentiment_data():
    """Create sample sentiment data."""
    return {
        'compound': 0.3,
        'positive': 0.6,
        'neutral': 0.3,
        'negative': 0.1,
        'trend_1h': 'Rising',
        'trend_4h': 'Stable',
        'volatility': 0.15,
        'tweet_count': 150,
        'engagement_rate': 0.08,
        'top_keywords': ['bitcoin', 'bullish', 'moon']
    }


class TestPromptBuilder:
    """Test the prompt builder functionality."""
    
    def test_prompt_builder_initialization(self):
        """Test prompt builder initialization."""
        builder = PromptBuilder()
        
        assert builder.base_prompt_template is not None
        assert builder.market_context_template is not None
        assert builder.technical_analysis_template is not None
        assert builder.sentiment_analysis_template is not None
        
        # Check that templates contain expected placeholders
        assert '{symbol}' in builder.market_context_template
        assert '{current_price}' in builder.market_context_template
        assert '{rsi}' in builder.technical_analysis_template
        assert '{sentiment_score}' in builder.sentiment_analysis_template
    
    def test_build_comprehensive_prompt(self, sample_features_data, sample_regime_info, sample_sentiment_data):
        """Test comprehensive prompt building."""
        builder = PromptBuilder()
        
        current_data = sample_features_data.iloc[-1]
        technical_indicators = {
            'ema_12': current_data['ema_12'],
            'rsi': current_data['rsi'],
            'macd': current_data['macd']
        }
        
        prompt = builder.build_comprehensive_prompt(
            symbol="BTC",
            current_data=current_data,
            regime_info=sample_regime_info,
            technical_indicators=technical_indicators,
            sentiment_data=sample_sentiment_data
        )
        
        assert isinstance(prompt, str)
        assert len(prompt) > 100  # Should be substantial
        assert 'BTC' in prompt
        assert 'Bull Market' in prompt
        assert 'TRADING RECOMMENDATION REQUEST' in prompt
    
    def test_build_quick_analysis_prompt(self):
        """Test quick analysis prompt building."""
        builder = PromptBuilder()
        
        prompt = builder.build_quick_analysis_prompt(
            symbol="BTC",
            current_price=50000.0,
            signal=TradingSignal.BUY,
            regime="Bull Market",
            confidence=0.8
        )
        
        assert isinstance(prompt, str)
        assert 'BTC' in prompt
        assert '50,000' in prompt
        assert 'BUY' in prompt
        assert 'Bull Market' in prompt
        assert '80.0%' in prompt
    
    def test_fallback_prompt(self, sample_features_data):
        """Test fallback prompt generation."""
        builder = PromptBuilder()
        
        current_data = sample_features_data.iloc[-1]
        prompt = builder._build_fallback_prompt("BTC", current_data)
        
        assert isinstance(prompt, str)
        assert 'BTC' in prompt
        assert 'trading recommendation' in prompt.lower()


class TestContextAnalyzer:
    """Test the context analyzer functionality."""
    
    def test_context_analyzer_initialization(self):
        """Test context analyzer initialization."""
        analyzer = ContextAnalyzer()
        
        assert analyzer.news_sources is not None
        assert len(analyzer.news_sources) > 0
        assert analyzer.context_cache == {}
        assert analyzer.cache_duration.total_seconds() > 0
    
    def test_gather_comprehensive_context(self, sample_features_data, sample_regime_info, sample_sentiment_data):
        """Test comprehensive context gathering."""
        analyzer = ContextAnalyzer()
        
        context = analyzer.gather_comprehensive_context(
            symbol="BTC",
            features_df=sample_features_data,
            regime_info=sample_regime_info,
            sentiment_data=sample_sentiment_data,
            include_news=False  # Skip news for testing
        )
        
        assert isinstance(context, dict)
        
        # Check main context sections
        expected_sections = [
            'current_data', 'market_summary', 'technical_analysis',
            'regime_analysis', 'market_structure', 'risk_analysis',
            'performance_context'
        ]
        
        for section in expected_sections:
            assert section in context, f"Missing section: {section}"
    
    def test_market_summary_analysis(self, sample_features_data):
        """Test market summary analysis."""
        analyzer = ContextAnalyzer()
        
        summary = analyzer._analyze_market_summary(sample_features_data)
        
        assert isinstance(summary, dict)
        assert 'current_price' in summary
        assert 'price_changes' in summary
        assert 'volume_analysis' in summary
        assert 'trading_range' in summary
        
        # Check price changes structure
        price_changes = summary['price_changes']
        assert isinstance(price_changes, dict)
        
        # Check volume analysis
        volume_analysis = summary['volume_analysis']
        assert 'current_volume' in volume_analysis
        assert 'volume_ratio' in volume_analysis
    
    def test_technical_indicators_analysis(self, sample_features_data):
        """Test technical indicators analysis."""
        analyzer = ContextAnalyzer()
        
        technical = analyzer._analyze_technical_indicators(sample_features_data)
        
        assert isinstance(technical, dict)
        assert 'trend_indicators' in technical
        assert 'momentum_indicators' in technical
        assert 'volatility_indicators' in technical
        assert 'overall_technical_bias' in technical
        
        # Check trend indicators
        trend_indicators = technical['trend_indicators']
        assert isinstance(trend_indicators, dict)
        
        # Check that bias is one of expected values
        bias = technical['overall_technical_bias']
        assert bias in ['Bullish', 'Bearish', 'Neutral']
    
    def test_regime_context_analysis(self, sample_features_data, sample_regime_info):
        """Test regime context analysis."""
        analyzer = ContextAnalyzer()
        
        regime_context = analyzer._analyze_regime_context(sample_regime_info, sample_features_data)
        
        assert isinstance(regime_context, dict)
        assert 'current_regime' in regime_context
        assert 'regime_confidence' in regime_context
        assert 'regime_probabilities' in regime_context
        
        # Check values
        assert regime_context['current_regime'] == 'Bull Market'
        assert regime_context['regime_confidence'] == 0.75
    
    def test_sentiment_context_analysis(self, sample_sentiment_data):
        """Test sentiment context analysis."""
        analyzer = ContextAnalyzer()
        
        sentiment_context = analyzer._analyze_sentiment_context(sample_sentiment_data)
        
        assert isinstance(sentiment_context, dict)
        assert 'current_sentiment' in sentiment_context
        assert 'sentiment_distribution' in sentiment_context
        assert 'sentiment_strength' in sentiment_context
        assert 'sentiment_label' in sentiment_context
        
        # Check sentiment label
        label = sentiment_context['sentiment_label']
        assert label in ['Bullish', 'Bearish', 'Neutral']
    
    def test_risk_context_analysis(self, sample_features_data):
        """Test risk context analysis."""
        analyzer = ContextAnalyzer()
        
        risk_context = analyzer._analyze_risk_context(sample_features_data)
        
        assert isinstance(risk_context, dict)
        assert 'volatility' in risk_context
        assert 'drawdown' in risk_context
        assert 'var_estimates' in risk_context
        
        # Check volatility structure
        volatility = risk_context['volatility']
        assert 'daily' in volatility
        assert 'regime' in volatility
        assert volatility['regime'] in ['High', 'Medium', 'Low']
    
    def test_trend_calculation(self, sample_features_data):
        """Test trend calculation utility."""
        analyzer = ContextAnalyzer()
        
        # Test with rising series
        rising_series = pd.Series([1, 2, 3, 4, 5])
        trend = analyzer._calculate_trend(rising_series)
        assert trend in ['Rising', 'Falling', 'Sideways']
        
        # Test with falling series
        falling_series = pd.Series([5, 4, 3, 2, 1])
        trend = analyzer._calculate_trend(falling_series)
        assert trend in ['Rising', 'Falling', 'Sideways']
        
        # Test with empty series
        empty_series = pd.Series([])
        trend = analyzer._calculate_trend(empty_series)
        assert trend == 'Neutral'


class TestLLMTradingAdvisor:
    """Test the LLM trading advisor."""
    
    def test_advisor_initialization(self):
        """Test advisor initialization."""
        # Test with local model disabled (to avoid model loading in tests)
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        assert advisor.prompt_builder is not None
        assert advisor.context_analyzer is not None
        assert advisor.model_name is not None
        assert not advisor.use_local_model
    
    def test_generate_trading_recommendation(self, sample_features_data, sample_regime_info, sample_sentiment_data):
        """Test trading recommendation generation."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        recommendation = advisor.generate_trading_recommendation(
            symbol="BTC",
            features_df=sample_features_data,
            regime_info=sample_regime_info,
            sentiment_data=sample_sentiment_data,
            include_detailed_analysis=False  # Use rule-based for testing
        )
        
        assert isinstance(recommendation, dict)
        assert 'recommendation' in recommendation
        assert 'metadata' in recommendation
        
        # Check recommendation structure
        rec = recommendation['recommendation']
        assert 'action' in rec
        assert 'confidence' in rec
        assert 'position_size' in rec
        assert 'risk_level' in rec
        
        # Check action is valid
        assert rec['action'] in ['BUY', 'SELL', 'HOLD']
        
        # Check confidence is in valid range
        assert 0 <= rec['confidence'] <= 1
        
        # Check risk level is valid
        assert rec['risk_level'] in ['Low', 'Medium', 'High']
    
    def test_rule_based_recommendation(self, sample_features_data, sample_regime_info):
        """Test rule-based recommendation generation."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        context = advisor.context_analyzer.gather_comprehensive_context(
            "BTC", sample_features_data, sample_regime_info, include_news=False
        )
        
        recommendation = advisor._generate_rule_based_recommendation(
            "BTC", sample_features_data, context, sample_regime_info
        )
        
        assert isinstance(recommendation, dict)
        assert 'recommendation' in recommendation
        assert 'reasoning' in recommendation
        assert 'risk_assessment' in recommendation
        assert 'monitoring_plan' in recommendation
        
        # Check reasoning structure
        reasoning = recommendation['reasoning']
        assert 'technical_score' in reasoning
        assert 'regime_score' in reasoning
        assert 'combined_score' in reasoning
    
    def test_technical_score_calculation(self, sample_features_data):
        """Test technical score calculation."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        current_data = sample_features_data.iloc[-1]
        score = advisor._calculate_technical_score(current_data)
        
        assert isinstance(score, float)
        assert -1 <= score <= 1  # Score should be normalized
    
    def test_regime_score_calculation(self, sample_regime_info):
        """Test regime score calculation."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        score = advisor._calculate_regime_score(sample_regime_info)
        
        assert isinstance(score, float)
        assert -1 <= score <= 1  # Score should be normalized
    
    def test_risk_assessment(self, sample_features_data, sample_regime_info):
        """Test risk assessment."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        context = advisor.context_analyzer.gather_comprehensive_context(
            "BTC", sample_features_data, sample_regime_info, include_news=False
        )
        
        risk_level = advisor._assess_risk_level(context)
        assert risk_level in ['Low', 'Medium', 'High']
        
        position_size = advisor._calculate_position_size(0.8, risk_level)
        assert isinstance(position_size, float)
        assert 0 < position_size <= 0.05  # Should be reasonable position size
    
    def test_price_targets_calculation(self, sample_features_data, sample_regime_info):
        """Test price targets calculation."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        context = advisor.context_analyzer.gather_comprehensive_context(
            "BTC", sample_features_data, sample_regime_info, include_news=False
        )
        
        current_price = sample_features_data.iloc[-1]['close']
        
        # Test BUY targets
        buy_targets = advisor._calculate_price_targets(current_price, 'BUY', context)
        assert isinstance(buy_targets, dict)
        assert 'entry' in buy_targets
        assert 'stop_loss' in buy_targets
        assert 'take_profit' in buy_targets
        
        # Test SELL targets
        sell_targets = advisor._calculate_price_targets(current_price, 'SELL', context)
        assert isinstance(sell_targets, dict)
        assert all(key in sell_targets for key in ['entry', 'stop_loss', 'take_profit'])
    
    def test_quick_recommendation(self):
        """Test quick recommendation generation."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        recommendation = advisor.generate_quick_recommendation(
            symbol="BTC",
            current_price=50000.0,
            signal=TradingSignal.BUY,
            regime="Bull Market",
            confidence=0.8
        )
        
        assert isinstance(recommendation, dict)
        assert 'recommendation' in recommendation
        assert 'confidence' in recommendation
        assert 'analysis' in recommendation
        assert 'timestamp' in recommendation
        
        assert recommendation['confidence'] == 0.8
    
    def test_availability_check(self):
        """Test availability check."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        # Should return False since we disabled local model
        assert not advisor.is_available()
    
    def test_empty_data_handling(self):
        """Test handling of empty data."""
        advisor = LLMTradingAdvisor(use_local_model=False)
        
        empty_df = pd.DataFrame()
        recommendation = advisor.generate_trading_recommendation(
            symbol="BTC",
            features_df=empty_df,
            regime_info={},
            include_detailed_analysis=False
        )
        
        assert 'error' in recommendation
