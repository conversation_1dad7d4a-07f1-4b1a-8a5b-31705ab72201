"""
Tests for configuration module.
"""

import pytest
from crypto_markov_trader.config import Settings


def test_settings_default_values():
    """Test that settings have expected default values."""
    settings = Settings()
    
    assert settings.hyperliquid_api_url == "https://api.hyperliquid.xyz"
    assert settings.hyperliquid_testnet is True
    assert settings.trading_symbols == ["BTC", "ETH", "SOL"]
    assert settings.hmm_n_components == 3
    assert settings.risk_percentage == 0.02


def test_settings_from_env(monkeypatch):
    """Test that settings can be loaded from environment variables."""
    monkeypatch.setenv("HYPERLIQUID_TESTNET", "false")
    monkeypatch.setenv("HMM_N_COMPONENTS", "5")
    monkeypatch.setenv("RISK_PERCENTAGE", "0.05")
    
    settings = Settings()
    
    assert settings.hyperliquid_testnet is False
    assert settings.hmm_n_components == 5
    assert settings.risk_percentage == 0.05
