"""
Tests for backtesting framework.
"""

import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_markov_trader.backtesting import BacktestEngine, PerformanceMetrics, StrategyOptimizer
from crypto_markov_trader.backtesting import Trade, Position, OrderType, OrderSide
from crypto_markov_trader.models import TradingSignal


@pytest.fixture
def sample_price_data():
    """Create sample price data for backtesting."""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
    
    # Generate realistic price data
    base_price = 50000
    returns = np.random.normal(0, 0.002, 1000)
    prices = [base_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, 1000)
    })
    
    df.set_index('timestamp', inplace=True)
    return df


@pytest.fixture
def sample_signals_data(sample_price_data):
    """Create sample trading signals."""
    np.random.seed(42)
    
    # Create signals that change periodically
    signals = []
    regimes = ['Bull Market', 'Bear Market', 'Sideways Market']
    
    for i in range(len(sample_price_data)):
        # Change signal every 100 periods
        signal_type = (i // 100) % 3
        
        if signal_type == 0:  # Bull
            signal = TradingSignal.BUY.value
            regime = 'Bull Market'
        elif signal_type == 1:  # Bear
            signal = TradingSignal.SELL.value
            regime = 'Bear Market'
        else:  # Sideways
            signal = TradingSignal.HOLD.value
            regime = 'Sideways Market'
        
        signals.append({
            'regime_state': signal_type,
            'regime_label': regime,
            'base_signal': signal,
            'final_signal': signal,
            'signal_strength': np.random.uniform(0.6, 0.9),
            'signal_description': f"{regime} conditions",
            'regime_changed': 1 if i % 100 == 0 else 0,
            'signal_changed': 1 if i % 100 == 0 else 0
        })
    
    signals_df = pd.DataFrame(signals, index=sample_price_data.index)
    return signals_df


class TestBacktestEngine:
    """Test the backtesting engine."""
    
    def test_engine_initialization(self):
        """Test engine initialization with different parameters."""
        # Default initialization
        engine1 = BacktestEngine()
        assert engine1.initial_capital == 10000.0
        assert engine1.commission_rate == 0.001
        assert engine1.capital == 10000.0
        assert engine1.position.size == 0.0
        
        # Custom initialization
        engine2 = BacktestEngine(
            initial_capital=50000.0,
            commission_rate=0.002,
            max_position_size=0.5
        )
        assert engine2.initial_capital == 50000.0
        assert engine2.commission_rate == 0.002
        assert engine2.max_position_size == 0.5
    
    def test_position_size_calculation(self):
        """Test position size calculation."""
        engine = BacktestEngine(initial_capital=10000.0, risk_per_trade=0.02)
        
        # Test BUY signal
        size = engine.calculate_position_size(
            TradingSignal.BUY, 
            price=50000, 
            signal_strength=0.8, 
            volatility=0.02
        )
        assert size > 0
        assert size * 50000 <= 10000.0  # Should not exceed capital
        
        # Test STRONG_BUY signal (should be larger)
        strong_size = engine.calculate_position_size(
            TradingSignal.STRONG_BUY, 
            price=50000, 
            signal_strength=0.8, 
            volatility=0.02
        )
        assert strong_size > size
        
        # Test HOLD signal (should be 0)
        hold_size = engine.calculate_position_size(
            TradingSignal.HOLD, 
            price=50000, 
            signal_strength=0.8, 
            volatility=0.02
        )
        assert hold_size == 0.0
    
    def test_trade_execution(self):
        """Test trade execution."""
        engine = BacktestEngine(initial_capital=10000.0)
        timestamp = datetime.now()
        
        # Test buy trade
        success = engine.execute_trade(
            timestamp=timestamp,
            side=OrderSide.BUY,
            size=0.1,
            price=50000,
            signal=TradingSignal.BUY,
            regime="Bull Market",
            signal_strength=0.8
        )
        
        assert success
        assert engine.position.size == 0.1
        assert engine.capital < 10000.0  # Should have decreased due to trade cost
        assert len(engine.trades) == 1
        
        # Test sell trade (should close position)
        success = engine.execute_trade(
            timestamp=timestamp + timedelta(minutes=1),
            side=OrderSide.SELL,
            size=0.1,
            price=51000,
            signal=TradingSignal.SELL,
            regime="Bear Market",
            signal_strength=0.7
        )
        
        assert success
        assert len(engine.trades) == 2  # Should have closing trade
        assert engine.position.size == 0.0  # Position should be closed
    
    def test_unrealized_pnl_calculation(self):
        """Test unrealized P&L calculation."""
        engine = BacktestEngine()
        
        # Open long position
        engine.position.size = 0.1
        engine.position.entry_price = 50000
        
        # Test with higher price (profit)
        engine.update_unrealized_pnl(52000)
        assert engine.position.unrealized_pnl > 0
        
        # Test with lower price (loss)
        engine.update_unrealized_pnl(48000)
        assert engine.position.unrealized_pnl < 0
        
        # Test with short position
        engine.position.size = -0.1
        engine.position.entry_price = 50000
        
        engine.update_unrealized_pnl(48000)  # Price down, short profits
        assert engine.position.unrealized_pnl > 0
    
    def test_backtest_run(self, sample_signals_data, sample_price_data):
        """Test complete backtest run."""
        engine = BacktestEngine(initial_capital=10000.0)
        
        # Run backtest (without classifier for simplicity)
        results = engine.run_backtest(sample_signals_data, sample_price_data, None)
        
        assert isinstance(results, dict)
        assert 'summary' in results
        assert 'equity_curve' in results
        assert 'trades' in results
        
        # Check summary metrics
        summary = results['summary']
        assert 'initial_capital' in summary
        assert 'final_equity' in summary
        assert 'total_return' in summary
        assert 'total_trades' in summary
        
        # Check equity curve
        equity_curve = results['equity_curve']
        assert not equity_curve.empty
        assert 'equity' in equity_curve.columns
        assert 'capital' in equity_curve.columns
    
    def test_reset_functionality(self):
        """Test engine reset."""
        engine = BacktestEngine()
        
        # Make some changes
        engine.capital = 5000
        engine.position.size = 0.5
        engine.trades.append(Trade(
            timestamp=datetime.now(),
            side=OrderSide.BUY,
            size=0.1,
            price=50000,
            commission=50,
            signal=TradingSignal.BUY,
            regime="Test",
            signal_strength=0.8
        ))
        
        # Reset
        engine.reset()
        
        # Check everything is reset
        assert engine.capital == engine.initial_capital
        assert engine.position.size == 0.0
        assert len(engine.trades) == 0
        assert len(engine.equity_curve) == 0


class TestPerformanceMetrics:
    """Test performance metrics calculation."""
    
    def test_metrics_initialization(self):
        """Test metrics initialization."""
        metrics = PerformanceMetrics()
        assert metrics.benchmark_return == 0.0
    
    def test_backtest_analysis(self, sample_signals_data, sample_price_data):
        """Test backtest results analysis."""
        # Run a simple backtest first
        engine = BacktestEngine(initial_capital=10000.0)
        backtest_results = engine.run_backtest(sample_signals_data, sample_price_data, None)
        
        # Analyze results
        metrics = PerformanceMetrics()
        analysis = metrics.analyze_backtest_results(backtest_results)
        
        assert isinstance(analysis, dict)
        
        # Check main analysis sections
        expected_sections = [
            'performance_summary', 'trade_analysis', 'regime_analysis',
            'risk_analysis', 'time_analysis', 'drawdown_analysis'
        ]
        
        for section in expected_sections:
            assert section in analysis
    
    def test_performance_summary_analysis(self):
        """Test performance summary analysis."""
        metrics = PerformanceMetrics()
        
        # Mock summary and advanced metrics
        summary = {
            'total_return_pct': 15.5,
            'total_return': 0.155,
            'max_drawdown_pct': 8.2,
            'total_trades': 50,
            'win_rate': 0.62,
            'winning_trades': 31,
            'losing_trades': 19
        }
        
        advanced_metrics = {
            'sharpe_ratio': 1.45,
            'sortino_ratio': 1.82,
            'calmar_ratio': 1.89,
            'volatility_annualized': 0.18,
            'profit_factor': 1.35
        }
        
        performance = metrics._analyze_performance_summary(summary, advanced_metrics)
        
        assert 'returns' in performance
        assert 'risk' in performance
        assert 'trading' in performance
        assert 'rating' in performance
        
        # Check specific metrics
        assert performance['returns']['total_return_pct'] == 15.5
        assert performance['returns']['sharpe_ratio'] == 1.45
        assert performance['risk']['max_drawdown_pct'] == 8.2
        assert performance['trading']['win_rate'] == 0.62
    
    def test_performance_rating(self):
        """Test performance rating calculation."""
        metrics = PerformanceMetrics()
        
        # Test excellent performance
        excellent_performance = {
            'returns': {'annualized_return': 0.25, 'sharpe_ratio': 2.5},
            'risk': {'max_drawdown_pct': 3.0},
            'trading': {'win_rate': 0.65}
        }
        rating = metrics._calculate_performance_rating(excellent_performance)
        assert rating == "Excellent"
        
        # Test poor performance
        poor_performance = {
            'returns': {'annualized_return': -0.05, 'sharpe_ratio': -0.5},
            'risk': {'max_drawdown_pct': 45.0},
            'trading': {'win_rate': 0.35}
        }
        rating = metrics._calculate_performance_rating(poor_performance)
        assert rating == "Poor"
    
    def test_report_generation(self):
        """Test performance report generation."""
        metrics = PerformanceMetrics()
        
        # Mock analysis data
        analysis = {
            'performance_summary': {
                'returns': {'total_return_pct': 15.5, 'annualized_return': 0.12, 'sharpe_ratio': 1.45},
                'risk': {'max_drawdown_pct': 8.2, 'profit_factor': 1.35},
                'trading': {'total_trades': 50, 'win_rate': 0.62},
                'rating': 'Good'
            },
            'trade_analysis': {
                'trade_distribution': {
                    'by_signal': {'BUY': 25, 'SELL': 20, 'HOLD': 5}
                }
            }
        }
        
        report = metrics.generate_performance_report(analysis)
        
        assert isinstance(report, str)
        assert "PERFORMANCE ANALYSIS REPORT" in report
        assert "Total Return: 15.50%" in report
        assert "Overall Rating: Good" in report


class TestStrategyOptimizer:
    """Test strategy optimization."""
    
    def test_optimizer_initialization(self):
        """Test optimizer initialization."""
        optimizer = StrategyOptimizer(max_workers=2)
        assert optimizer.max_workers == 2
        assert isinstance(optimizer.performance_metrics, PerformanceMetrics)
    
    def test_parameter_combination_generation(self):
        """Test parameter combination generation."""
        optimizer = StrategyOptimizer()
        
        parameter_ranges = {
            'n_components': [2, 3],
            'commission_rate': [0.001, 0.002],
            'risk_per_trade': [0.01, 0.02]
        }
        
        combinations = optimizer._generate_parameter_combinations(parameter_ranges)
        
        assert len(combinations) == 8  # 2 * 2 * 2
        
        # Check first combination
        assert combinations[0]['n_components'] in [2, 3]
        assert combinations[0]['commission_rate'] in [0.001, 0.002]
        assert combinations[0]['risk_per_trade'] in [0.01, 0.02]
    
    def test_parameter_sensitivity_analysis(self):
        """Test parameter sensitivity analysis."""
        optimizer = StrategyOptimizer()
        
        # Mock results
        results = [
            {'parameters': {'n_components': 2, 'risk_per_trade': 0.01}, 'sharpe_ratio': 1.2},
            {'parameters': {'n_components': 3, 'risk_per_trade': 0.01}, 'sharpe_ratio': 1.5},
            {'parameters': {'n_components': 2, 'risk_per_trade': 0.02}, 'sharpe_ratio': 1.1},
            {'parameters': {'n_components': 3, 'risk_per_trade': 0.02}, 'sharpe_ratio': 1.8},
        ]
        
        parameter_ranges = {
            'n_components': [2, 3],
            'risk_per_trade': [0.01, 0.02]
        }
        
        sensitivity = optimizer._analyze_parameter_sensitivity(results, parameter_ranges)
        
        assert 'n_components' in sensitivity
        assert 'risk_per_trade' in sensitivity
        
        # Check sensitivity structure
        for param in sensitivity.values():
            assert 'correlation_with_performance' in param
            assert 'best_value' in param
            assert 'worst_value' in param
