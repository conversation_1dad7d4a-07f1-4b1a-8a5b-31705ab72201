"""
Tests for HMM models and regime classification.
"""

import pytest
import pandas as pd
import numpy as np
import tempfile
import os
from datetime import datetime, timedelta

from crypto_markov_trader.models import MarkovRegimeModel, RegimeClassifier, TradingSignal, ModelTrainer
from crypto_markov_trader.feature_engineering import FeaturePipeline


@pytest.fixture
def sample_features_data():
    """Create sample features data for testing."""
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=500, freq='1min')
    
    # Create realistic feature data
    n_samples = len(dates)
    
    # Price data
    base_price = 50000
    returns = np.random.normal(0, 0.002, n_samples)
    prices = [base_price]
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Technical indicators
    data = {
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, n_samples),
        
        # Technical indicators
        'ema_12': np.random.uniform(49000, 51000, n_samples),
        'ema_26': np.random.uniform(49000, 51000, n_samples),
        'rsi': np.random.uniform(20, 80, n_samples),
        'macd': np.random.normal(0, 100, n_samples),
        'bb_width': np.random.uniform(0.01, 0.05, n_samples),
        'atr': np.random.uniform(100, 500, n_samples),
        'volume_ratio': np.random.uniform(0.5, 2.0, n_samples),
        
        # Sentiment features (mock)
        'sentiment_compound_mean': np.random.normal(0, 0.3, n_samples),
        'sentiment_positive_mean': np.random.uniform(0.2, 0.8, n_samples),
        
        # Time features
        'hour': [d.hour for d in dates],
        'day_of_week': [d.dayofweek for d in dates],
        'is_weekend': [(d.dayofweek >= 5) for d in dates]
    }
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    
    return df


@pytest.fixture
def trained_hmm_model(sample_features_data):
    """Create a trained HMM model for testing."""
    model = MarkovRegimeModel(n_components=3, n_iter=10)  # Reduced iterations for testing
    
    # Select features for training
    feature_cols = ['ema_12', 'ema_26', 'rsi', 'macd', 'bb_width', 'atr']
    model.fit(sample_features_data, feature_cols)
    
    return model


class TestMarkovRegimeModel:
    """Test the HMM model implementation."""
    
    def test_model_initialization(self):
        """Test model initialization with different parameters."""
        # Default initialization
        model1 = MarkovRegimeModel()
        assert model1.n_components == 3  # Default from settings
        assert model1.model is not None
        assert not model1.is_fitted
        
        # Custom initialization
        model2 = MarkovRegimeModel(n_components=2, covariance_type='diag')
        assert model2.n_components == 2
        assert model2.covariance_type == 'diag'
        assert len(model2.regime_labels) == 2
    
    def test_prepare_features(self, sample_features_data):
        """Test feature preparation."""
        model = MarkovRegimeModel()
        
        # Test with specific feature columns
        feature_cols = ['ema_12', 'rsi', 'macd']
        X = model.prepare_features(sample_features_data, feature_cols)
        
        assert X.shape[0] == len(sample_features_data)
        assert X.shape[1] == len(feature_cols)
        assert not np.isnan(X).any()
        assert not np.isinf(X).any()
        
        # Test with automatic feature selection
        X_auto = model.prepare_features(sample_features_data)
        assert X_auto.shape[0] == len(sample_features_data)
        assert X_auto.shape[1] > 0
    
    def test_model_fitting(self, sample_features_data):
        """Test model training."""
        model = MarkovRegimeModel(n_components=2, n_iter=5)  # Quick training
        
        feature_cols = ['ema_12', 'rsi', 'macd', 'bb_width']
        model.fit(sample_features_data, feature_cols)
        
        assert model.is_fitted
        assert len(model.feature_names) == len(feature_cols)
        assert model.scaler is not None
        assert len(model.training_history['log_likelihood']) > 0
    
    def test_model_prediction(self, trained_hmm_model, sample_features_data):
        """Test model prediction."""
        # Test state prediction
        states = trained_hmm_model.predict(sample_features_data)
        
        assert len(states) == len(sample_features_data)
        assert all(0 <= state < trained_hmm_model.n_components for state in states)
        
        # Test probability prediction
        probabilities = trained_hmm_model.predict_proba(sample_features_data)
        
        assert probabilities.shape == (len(sample_features_data), trained_hmm_model.n_components)
        assert np.allclose(probabilities.sum(axis=1), 1.0)  # Probabilities sum to 1
        assert (probabilities >= 0).all()  # All probabilities non-negative
    
    def test_regime_summary(self, trained_hmm_model, sample_features_data):
        """Test regime summary generation."""
        summary = trained_hmm_model.get_regime_summary(sample_features_data)
        
        assert not summary.empty
        assert len(summary) == len(sample_features_data)
        assert 'regime_state' in summary.columns
        assert 'regime_label' in summary.columns
        
        # Check probability columns exist
        prob_cols = [col for col in summary.columns if col.startswith('prob_')]
        assert len(prob_cols) == trained_hmm_model.n_components
    
    def test_model_save_load(self, trained_hmm_model):
        """Test model saving and loading."""
        with tempfile.TemporaryDirectory() as temp_dir:
            model_path = os.path.join(temp_dir, 'test_model.joblib')
            
            # Save model
            trained_hmm_model.save_model(model_path)
            assert os.path.exists(model_path)
            
            # Load model
            new_model = MarkovRegimeModel()
            new_model.load_model(model_path)
            
            assert new_model.is_fitted
            assert new_model.n_components == trained_hmm_model.n_components
            assert new_model.feature_names == trained_hmm_model.feature_names
            assert new_model.regime_labels == trained_hmm_model.regime_labels


class TestRegimeClassifier:
    """Test the regime classifier."""
    
    def test_classifier_initialization(self, trained_hmm_model):
        """Test classifier initialization."""
        classifier = RegimeClassifier(trained_hmm_model)
        
        assert classifier.hmm_model == trained_hmm_model
        assert len(classifier.signal_rules) == trained_hmm_model.n_components
        
        # Check signal rules structure
        for regime_label, rules in classifier.signal_rules.items():
            assert 'signal' in rules
            assert 'confidence_threshold' in rules
            assert 'description' in rules
            assert isinstance(rules['signal'], TradingSignal)
    
    def test_signal_generation(self, trained_hmm_model, sample_features_data):
        """Test trading signal generation."""
        classifier = RegimeClassifier(trained_hmm_model)
        
        signals_df = classifier.generate_signals(sample_features_data)
        
        assert not signals_df.empty
        assert len(signals_df) == len(sample_features_data)
        
        # Check required columns
        required_cols = ['regime_state', 'regime_label', 'base_signal', 'final_signal', 
                        'signal_description', 'signal_strength']
        for col in required_cols:
            assert col in signals_df.columns
        
        # Check signal values are valid
        valid_signals = [signal.value for signal in TradingSignal]
        assert all(signal in valid_signals for signal in signals_df['final_signal'])
    
    def test_current_signal(self, trained_hmm_model, sample_features_data):
        """Test current signal extraction."""
        classifier = RegimeClassifier(trained_hmm_model)
        
        # Test with recent data
        recent_data = sample_features_data.tail(10)
        current_signal = classifier.get_current_signal(recent_data)
        
        assert isinstance(current_signal, dict)
        assert 'regime' in current_signal
        assert 'signal' in current_signal
        assert 'signal_name' in current_signal
        assert 'signal_strength' in current_signal
        assert 'timestamp' in current_signal
    
    def test_signal_performance_analysis(self, trained_hmm_model, sample_features_data):
        """Test signal performance analysis."""
        classifier = RegimeClassifier(trained_hmm_model)
        
        signals_df = classifier.generate_signals(sample_features_data)
        price_data = sample_features_data[['open', 'high', 'low', 'close', 'volume']]
        
        performance = classifier.analyze_signal_performance(signals_df, price_data)
        
        assert isinstance(performance, dict)
        # Should have performance metrics for different signal types
        for signal_name, metrics in performance.items():
            assert 'count' in metrics
            assert 'mean_return' in metrics
            assert 'win_rate' in metrics
    
    def test_regime_transitions(self, trained_hmm_model, sample_features_data):
        """Test regime transition analysis."""
        classifier = RegimeClassifier(trained_hmm_model)
        
        signals_df = classifier.generate_signals(sample_features_data)
        transitions = classifier.get_regime_transitions(signals_df)
        
        # Should be a DataFrame (might be empty if no transitions)
        assert isinstance(transitions, pd.DataFrame)
        
        if not transitions.empty:
            expected_cols = ['from_regime', 'to_regime', 'transition_type', 'periods_since_last']
            for col in expected_cols:
                assert col in transitions.columns


class TestModelTrainer:
    """Test the complete model training pipeline."""
    
    def test_trainer_initialization(self):
        """Test trainer initialization."""
        with tempfile.TemporaryDirectory() as temp_dir:
            trainer = ModelTrainer(data_dir=temp_dir, models_dir=temp_dir)
            
            assert trainer.data_dir == temp_dir
            assert trainer.models_dir == temp_dir
            assert isinstance(trainer.feature_pipeline, FeaturePipeline)
            assert isinstance(trainer.training_config, dict)
    
    def test_feature_selection(self, sample_features_data):
        """Test feature selection."""
        with tempfile.TemporaryDirectory() as temp_dir:
            trainer = ModelTrainer(data_dir=temp_dir)
            
            selected_features = trainer.select_features(sample_features_data)
            
            assert isinstance(selected_features, list)
            assert len(selected_features) > 0
            
            # All selected features should exist in the data
            for feature in selected_features:
                assert feature in sample_features_data.columns
    
    def test_model_training(self, sample_features_data):
        """Test model training process."""
        with tempfile.TemporaryDirectory() as temp_dir:
            trainer = ModelTrainer(data_dir=temp_dir)
            
            selected_features = ['ema_12', 'rsi', 'macd', 'bb_width']
            
            # Train without cross-validation for speed
            model = trainer.train_model(
                sample_features_data, 
                selected_features, 
                n_components=2,
                cross_validate=False
            )
            
            assert model is not None
            assert model.is_fitted
            assert len(model.feature_names) == len(selected_features)
    
    def test_model_evaluation(self, sample_features_data):
        """Test model evaluation."""
        with tempfile.TemporaryDirectory() as temp_dir:
            trainer = ModelTrainer(data_dir=temp_dir)
            
            # Create and train a simple model
            model = MarkovRegimeModel(n_components=2, n_iter=5)
            feature_cols = ['ema_12', 'rsi', 'macd']
            model.fit(sample_features_data, feature_cols)
            
            # Extract price data
            price_df = sample_features_data[['open', 'high', 'low', 'close', 'volume']]
            
            # Evaluate model
            evaluation = trainer.evaluate_model(model, sample_features_data, price_df)
            
            assert isinstance(evaluation, dict)
            assert 'model_info' in evaluation
            assert 'regime_distribution' in evaluation
            assert 'signal_performance' in evaluation
