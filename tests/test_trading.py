"""
Tests for live trading components.
"""

import pytest
import asyncio
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_markov_trader.trading import (
    ExecutionEngine, PortfolioManager, RiskManager, LiveTrader, TradingMonitor
)
from crypto_markov_trader.trading.execution_engine import Order, OrderType, OrderStatus
from crypto_markov_trader.trading.portfolio_manager import Position
from crypto_markov_trader.trading.risk_manager import RiskLevel, RiskAlert
from crypto_markov_trader.models import TradingSignal


@pytest.fixture
def execution_engine():
    """Create execution engine for testing."""
    return ExecutionEngine()


@pytest.fixture
def portfolio_manager():
    """Create portfolio manager for testing."""
    return PortfolioManager(initial_capital=100000.0)


@pytest.fixture
def risk_manager(portfolio_manager):
    """Create risk manager for testing."""
    return RiskManager(portfolio_manager)


@pytest.fixture
def live_trader():
    """Create live trader for testing."""
    return LiveTrader(symbols=['BTC', 'ETH'], initial_capital=100000.0)


class TestExecutionEngine:
    """Test the execution engine."""
    
    def test_engine_initialization(self, execution_engine):
        """Test engine initialization."""
        assert execution_engine.exchanges is not None
        assert execution_engine.active_orders == {}
        assert execution_engine.order_history == []
        assert execution_engine.execution_stats['total_orders'] == 0
    
    @pytest.mark.asyncio
    async def test_submit_market_order(self, execution_engine):
        """Test market order submission."""
        order = await execution_engine.submit_order(
            symbol="BTC",
            side="buy",
            quantity=0.1,
            order_type=OrderType.MARKET
        )
        
        assert isinstance(order, Order)
        assert order.symbol == "BTC"
        assert order.side == "buy"
        assert order.quantity == 0.1
        assert order.type == OrderType.MARKET
        
        # Check if order was processed
        assert order.id in execution_engine.active_orders
        assert len(execution_engine.order_history) == 1
    
    @pytest.mark.asyncio
    async def test_submit_limit_order(self, execution_engine):
        """Test limit order submission."""
        order = await execution_engine.submit_order(
            symbol="BTC",
            side="sell",
            quantity=0.05,
            order_type=OrderType.LIMIT,
            price=55000.0
        )
        
        assert order.type == OrderType.LIMIT
        assert order.price == 55000.0
        assert order.status in [OrderStatus.SUBMITTED, OrderStatus.FILLED]
    
    @pytest.mark.asyncio
    async def test_cancel_order(self, execution_engine):
        """Test order cancellation."""
        # Submit an order first
        order = await execution_engine.submit_order(
            symbol="BTC",
            side="buy",
            quantity=0.1,
            order_type=OrderType.LIMIT,
            price=45000.0
        )
        
        # Cancel the order
        success = await execution_engine.cancel_order(order.id)
        assert success
        
        # Check order status
        cancelled_order = await execution_engine.get_order_status(order.id)
        assert cancelled_order.status == OrderStatus.CANCELLED
    
    def test_get_active_orders(self, execution_engine):
        """Test getting active orders."""
        active_orders = execution_engine.get_active_orders()
        assert isinstance(active_orders, list)
        
        # Test symbol filtering
        btc_orders = execution_engine.get_active_orders("BTC")
        assert isinstance(btc_orders, list)
    
    def test_execution_stats(self, execution_engine):
        """Test execution statistics."""
        stats = execution_engine.get_execution_stats()
        
        assert 'total_orders' in stats
        assert 'successful_orders' in stats
        assert 'failed_orders' in stats
        assert 'total_volume' in stats
        assert 'total_fees' in stats


class TestPortfolioManager:
    """Test the portfolio manager."""
    
    def test_manager_initialization(self, portfolio_manager):
        """Test portfolio manager initialization."""
        assert portfolio_manager.initial_capital == 100000.0
        assert portfolio_manager.cash_balance == 100000.0
        assert portfolio_manager.positions == {}
        assert portfolio_manager.total_realized_pnl == 0.0
    
    def test_update_position_new(self, portfolio_manager):
        """Test updating a new position."""
        portfolio_manager.update_position(
            symbol="BTC",
            quantity_change=0.1,
            price=50000.0,
            fees=50.0
        )
        
        position = portfolio_manager.get_position("BTC")
        assert position is not None
        assert position.quantity == 0.1
        assert position.average_price == 50000.0
        assert portfolio_manager.cash_balance < 100000.0  # Should decrease
    
    def test_update_position_existing(self, portfolio_manager):
        """Test updating an existing position."""
        # Create initial position
        portfolio_manager.update_position("BTC", 0.1, 50000.0, 50.0)
        
        # Add to position
        portfolio_manager.update_position("BTC", 0.05, 52000.0, 26.0)
        
        position = portfolio_manager.get_position("BTC")
        assert position.quantity == 0.15
        # Average price should be weighted average
        expected_avg = (0.1 * 50000 + 0.05 * 52000) / 0.15
        assert abs(position.average_price - expected_avg) < 0.01
    
    def test_close_position(self, portfolio_manager):
        """Test closing a position."""
        # Create position
        portfolio_manager.update_position("BTC", 0.1, 50000.0, 50.0)
        
        # Close position
        portfolio_manager.update_position("BTC", -0.1, 55000.0, 55.0)
        
        # Position should be removed
        position = portfolio_manager.get_position("BTC")
        assert position is None
        
        # Should have realized P&L
        assert portfolio_manager.total_realized_pnl > 0
    
    def test_update_market_prices(self, portfolio_manager):
        """Test market price updates."""
        # Create position
        portfolio_manager.update_position("BTC", 0.1, 50000.0, 50.0)
        
        # Update market prices
        portfolio_manager.update_market_prices({"BTC": 55000.0})
        
        position = portfolio_manager.get_position("BTC")
        assert position.market_value == 0.1 * 55000.0
        assert position.unrealized_pnl == 0.1 * (55000.0 - 50000.0)
    
    def test_portfolio_value(self, portfolio_manager):
        """Test portfolio value calculation."""
        initial_value = portfolio_manager.get_portfolio_value()
        assert initial_value == 100000.0
        
        # Add position
        portfolio_manager.update_position("BTC", 0.1, 50000.0, 50.0)
        portfolio_manager.update_market_prices({"BTC": 55000.0})
        
        new_value = portfolio_manager.get_portfolio_value()
        assert new_value > initial_value  # Should increase due to unrealized gain
    
    def test_position_size_calculation(self, portfolio_manager):
        """Test position size calculation."""
        position_size = portfolio_manager.calculate_position_size(
            symbol="BTC",
            signal_strength=0.8,
            current_price=50000.0,
            volatility=0.02
        )
        
        assert position_size > 0
        assert position_size * 50000 <= 100000.0  # Should not exceed portfolio value
    
    def test_portfolio_summary(self, portfolio_manager):
        """Test portfolio summary generation."""
        summary = portfolio_manager.get_portfolio_summary()
        
        assert 'total_value' in summary
        assert 'cash_balance' in summary
        assert 'positions_value' in summary
        assert 'total_return' in summary
        assert 'realized_pnl' in summary
        assert 'unrealized_pnl' in summary
    
    def test_risk_limits_check(self, portfolio_manager):
        """Test risk limits checking."""
        risk_check = portfolio_manager.check_risk_limits()
        
        assert 'violations' in risk_check
        assert 'warnings' in risk_check
        assert 'risk_score' in risk_check
        assert isinstance(risk_check['violations'], list)
        assert isinstance(risk_check['warnings'], list)


class TestRiskManager:
    """Test the risk manager."""
    
    def test_manager_initialization(self, risk_manager):
        """Test risk manager initialization."""
        assert risk_manager.portfolio_manager is not None
        assert risk_manager.risk_limits is not None
        assert risk_manager.circuit_breakers is not None
        assert risk_manager.monitoring_enabled
        assert not risk_manager.emergency_stop
    
    def test_pre_trade_risk_check_approved(self, risk_manager):
        """Test pre-trade risk check that should be approved."""
        approved, warnings = risk_manager.check_pre_trade_risk(
            symbol="BTC",
            side="buy",
            quantity=0.01,  # Small position
            price=50000.0,
            signal_strength=0.8
        )
        
        assert approved
        assert isinstance(warnings, list)
    
    def test_pre_trade_risk_check_rejected(self, risk_manager):
        """Test pre-trade risk check that should be rejected."""
        approved, warnings = risk_manager.check_pre_trade_risk(
            symbol="BTC",
            side="buy",
            quantity=10.0,  # Very large position
            price=50000.0,
            signal_strength=0.3  # Low signal strength
        )
        
        assert not approved
        assert len(warnings) > 0
    
    def test_portfolio_risk_monitoring(self, risk_manager):
        """Test portfolio risk monitoring."""
        alerts = risk_manager.monitor_portfolio_risk()
        
        assert isinstance(alerts, list)
        # With default portfolio, should have no alerts
        assert len(alerts) == 0
    
    def test_var_calculation(self, risk_manager):
        """Test VaR calculation."""
        # Add some portfolio history first
        for i in range(50):
            snapshot = risk_manager.portfolio_manager.create_snapshot()
            if snapshot:
                risk_manager.portfolio_manager.portfolio_history.append(snapshot)
        
        var_95 = risk_manager.calculate_var(0.95)
        var_99 = risk_manager.calculate_var(0.99)
        
        assert isinstance(var_95, float)
        assert isinstance(var_99, float)
        assert var_95 >= 0
        assert var_99 >= 0
    
    def test_risk_score_calculation(self, risk_manager):
        """Test risk score calculation."""
        risk_score = risk_manager.get_risk_score()
        
        assert isinstance(risk_score, float)
        assert 0 <= risk_score <= 100
    
    def test_risk_report_generation(self, risk_manager):
        """Test risk report generation."""
        report = risk_manager.get_risk_report()
        
        assert 'timestamp' in report
        assert 'risk_score' in report
        assert 'emergency_stop' in report
        assert 'portfolio_summary' in report
        assert 'risk_metrics' in report
        assert 'recent_alerts' in report
    
    def test_emergency_stop_reset(self, risk_manager):
        """Test emergency stop reset."""
        # Activate emergency stop
        risk_manager.emergency_stop = True
        assert risk_manager.emergency_stop
        
        # Reset emergency stop
        success = risk_manager.reset_emergency_stop()
        assert success
        assert not risk_manager.emergency_stop
    
    def test_risk_limits_update(self, risk_manager):
        """Test risk limits update."""
        new_limits = {
            'max_portfolio_drawdown': 0.20,
            'max_daily_loss': 0.08
        }
        
        risk_manager.update_risk_limits(new_limits)
        
        assert risk_manager.risk_limits['max_portfolio_drawdown'] == 0.20
        assert risk_manager.risk_limits['max_daily_loss'] == 0.08


class TestLiveTrader:
    """Test the live trader."""
    
    def test_trader_initialization(self, live_trader):
        """Test live trader initialization."""
        assert live_trader.symbols == ['BTC', 'ETH']
        assert live_trader.initial_capital == 100000.0
        assert live_trader.execution_engine is not None
        assert live_trader.portfolio_manager is not None
        assert live_trader.risk_manager is not None
        assert not live_trader.is_running
        assert live_trader.trading_enabled
    
    def test_get_status(self, live_trader):
        """Test getting trader status."""
        status = live_trader.get_status()
        
        assert 'is_running' in status
        assert 'trading_enabled' in status
        assert 'emergency_stop' in status
        assert 'symbols' in status
        assert 'portfolio_value' in status
        assert 'trading_stats' in status
    
    def test_enable_disable_trading(self, live_trader):
        """Test enabling and disabling trading."""
        # Initially enabled
        assert live_trader.trading_enabled
        
        # Disable trading
        live_trader.disable_trading()
        assert not live_trader.trading_enabled
        
        # Enable trading
        live_trader.enable_trading()
        assert live_trader.trading_enabled
    
    @pytest.mark.asyncio
    async def test_emergency_stop(self, live_trader):
        """Test emergency stop functionality."""
        await live_trader.emergency_stop()
        
        assert not live_trader.trading_enabled
        assert live_trader.risk_manager.emergency_stop
    
    @pytest.mark.asyncio
    async def test_get_recent_features(self, live_trader):
        """Test getting recent features."""
        features_df = await live_trader._get_recent_features("BTC")
        
        assert isinstance(features_df, pd.DataFrame)
        assert not features_df.empty
        assert 'close' in features_df.columns
        assert 'volume' in features_df.columns


class TestTradingMonitor:
    """Test the trading monitor."""
    
    def test_monitor_initialization(self, live_trader):
        """Test monitor initialization."""
        monitor = TradingMonitor(live_trader)
        
        assert monitor.live_trader == live_trader
        assert monitor.alerts == []
        assert monitor.monitoring_enabled
        assert monitor.thresholds is not None
    
    def test_get_monitoring_dashboard(self, live_trader):
        """Test monitoring dashboard generation."""
        monitor = TradingMonitor(live_trader)
        dashboard = monitor.get_monitoring_dashboard()
        
        assert 'timestamp' in dashboard
        assert 'monitoring_enabled' in dashboard
        assert 'system_health' in dashboard
        assert 'alert_summary' in dashboard
        assert 'performance_metrics' in dashboard
        assert 'thresholds' in dashboard
    
    def test_update_thresholds(self, live_trader):
        """Test updating monitoring thresholds."""
        monitor = TradingMonitor(live_trader)
        
        new_thresholds = {
            'max_daily_loss_pct': 0.08,
            'max_risk_score': 85
        }
        
        monitor.update_thresholds(new_thresholds)
        
        assert monitor.thresholds['max_daily_loss_pct'] == 0.08
        assert monitor.thresholds['max_risk_score'] == 85
    
    def test_get_performance_history(self, live_trader):
        """Test getting performance history."""
        monitor = TradingMonitor(live_trader)
        
        # Add some mock performance data
        for i in range(10):
            performance_data = {
                'timestamp': datetime.now() - timedelta(hours=i),
                'portfolio_value': 100000 + i * 1000,
                'daily_pnl': i * 100,
                'total_return': i * 0.01,
                'max_drawdown': 0.0,
                'num_positions': 1,
                'risk_score': 50.0
            }
            monitor.performance_history.append(performance_data)
        
        history = monitor.get_performance_history(hours=12)
        assert isinstance(history, list)
        assert len(history) > 0
    
    def test_add_alert_handler(self, live_trader):
        """Test adding custom alert handler."""
        monitor = TradingMonitor(live_trader)
        
        def custom_handler(alert):
            pass
        
        initial_count = len(monitor.alert_handlers)
        monitor.add_alert_handler(custom_handler)
        
        assert len(monitor.alert_handlers) == initial_count + 1
