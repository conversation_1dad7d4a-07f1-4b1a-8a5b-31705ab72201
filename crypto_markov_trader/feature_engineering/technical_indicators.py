"""
Technical indicators calculation using pandas-ta.
"""

import pandas as pd
import pandas_ta as ta
from typing import Dict, List, Optional
from loguru import logger

from ..config import settings


class TechnicalIndicators:
    """Calculate technical indicators for market data."""
    
    def __init__(self):
        self.indicators_config = {
            # Trend indicators
            'ema_fast': {'length': 12},
            'ema_slow': {'length': 26},
            'ema_signal': {'length': 9},
            'sma_20': {'length': 20},
            'sma_50': {'length': 50},
            
            # Momentum indicators
            'rsi': {'length': 14},
            'stoch': {'k': 14, 'd': 3},
            'williams_r': {'length': 14},
            
            # Volatility indicators
            'bbands': {'length': 20, 'std': 2},
            'atr': {'length': 14},
            
            # Volume indicators
            'obv': {},
            'ad': {},  # Accumulation/Distribution
            
            # Other indicators
            'macd': {'fast': 12, 'slow': 26, 'signal': 9},
            'adx': {'length': 14},
        }
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate all technical indicators for the given OHLCV data.
        
        Args:
            df: DataFrame with OHLCV data (columns: open, high, low, close, volume)
            
        Returns:
            DataFrame with original data plus technical indicators
        """
        if df.empty:
            logger.warning("Empty DataFrame provided for indicator calculation")
            return df.copy()
        
        try:
            # Ensure required columns exist
            required_cols = ['open', 'high', 'low', 'close', 'volume']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.error(f"Missing required columns: {missing_cols}")
                return df.copy()
            
            # Create a copy to avoid modifying original data
            result_df = df.copy()
            
            # Calculate trend indicators
            result_df = self._add_trend_indicators(result_df)
            
            # Calculate momentum indicators
            result_df = self._add_momentum_indicators(result_df)
            
            # Calculate volatility indicators
            result_df = self._add_volatility_indicators(result_df)
            
            # Calculate volume indicators
            result_df = self._add_volume_indicators(result_df)
            
            # Calculate composite indicators
            result_df = self._add_composite_indicators(result_df)
            
            # Add custom features
            result_df = self._add_custom_features(result_df)
            
            logger.info(f"Calculated technical indicators: {len(result_df.columns)} total columns")
            return result_df
            
        except Exception as e:
            logger.error(f"Error calculating technical indicators: {e}")
            return df.copy()
    
    def _add_trend_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add trend-following indicators."""
        try:
            # Exponential Moving Averages
            df['ema_12'] = ta.ema(df['close'], length=self.indicators_config['ema_fast']['length'])
            df['ema_26'] = ta.ema(df['close'], length=self.indicators_config['ema_slow']['length'])
            df['ema_9'] = ta.ema(df['close'], length=self.indicators_config['ema_signal']['length'])
            
            # Simple Moving Averages
            df['sma_20'] = ta.sma(df['close'], length=self.indicators_config['sma_20']['length'])
            df['sma_50'] = ta.sma(df['close'], length=self.indicators_config['sma_50']['length'])
            
            # Price position relative to moving averages (handle NaN values)
            df['price_above_ema12'] = ((df['close'] > df['ema_12']) & df['ema_12'].notna() & df['close'].notna()).astype(int)
            df['price_above_sma20'] = ((df['close'] > df['sma_20']) & df['sma_20'].notna() & df['close'].notna()).astype(int)
            df['price_above_sma50'] = ((df['close'] > df['sma_50']) & df['sma_50'].notna() & df['close'].notna()).astype(int)

            # Moving average crossovers (handle NaN values)
            df['ema_cross_signal'] = ((df['ema_12'] > df['ema_26']) & df['ema_12'].notna() & df['ema_26'].notna()).astype(int)
            df['sma_cross_signal'] = ((df['sma_20'] > df['sma_50']) & df['sma_20'].notna() & df['sma_50'].notna()).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating trend indicators: {e}")
            return df
    
    def _add_momentum_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add momentum indicators."""
        try:
            # RSI
            df['rsi'] = ta.rsi(df['close'], length=self.indicators_config['rsi']['length'])
            df['rsi_overbought'] = (df['rsi'] > 70).astype(int)
            df['rsi_oversold'] = (df['rsi'] < 30).astype(int)
            
            # Stochastic Oscillator
            stoch = ta.stoch(df['high'], df['low'], df['close'], 
                           k=self.indicators_config['stoch']['k'],
                           d=self.indicators_config['stoch']['d'])
            if stoch is not None and not stoch.empty:
                df['stoch_k'] = stoch.iloc[:, 0]  # %K
                df['stoch_d'] = stoch.iloc[:, 1]  # %D
                df['stoch_overbought'] = (df['stoch_k'] > 80).astype(int)
                df['stoch_oversold'] = (df['stoch_k'] < 20).astype(int)
            
            # Williams %R
            df['williams_r'] = ta.willr(df['high'], df['low'], df['close'], 
                                      length=self.indicators_config['williams_r']['length'])
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating momentum indicators: {e}")
            return df
    
    def _add_volatility_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volatility indicators."""
        try:
            # Bollinger Bands
            bbands = ta.bbands(df['close'], 
                             length=self.indicators_config['bbands']['length'],
                             std=self.indicators_config['bbands']['std'])
            if bbands is not None and not bbands.empty:
                df['bb_lower'] = bbands.iloc[:, 0]
                df['bb_mid'] = bbands.iloc[:, 1]
                df['bb_upper'] = bbands.iloc[:, 2]
                df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_mid']
                df['bb_position'] = (df['close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
            
            # Average True Range
            df['atr'] = ta.atr(df['high'], df['low'], df['close'], 
                             length=self.indicators_config['atr']['length'])
            df['atr_percent'] = df['atr'] / df['close'] * 100
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating volatility indicators: {e}")
            return df
    
    def _add_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add volume-based indicators."""
        try:
            # On-Balance Volume
            df['obv'] = ta.obv(df['close'], df['volume'])
            
            # Accumulation/Distribution Line
            df['ad'] = ta.ad(df['high'], df['low'], df['close'], df['volume'])
            
            # Volume Moving Average
            df['volume_sma'] = ta.sma(df['volume'], length=20)
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating volume indicators: {e}")
            return df
    
    def _add_composite_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add composite indicators like MACD."""
        try:
            # MACD
            macd = ta.macd(df['close'], 
                          fast=self.indicators_config['macd']['fast'],
                          slow=self.indicators_config['macd']['slow'],
                          signal=self.indicators_config['macd']['signal'])
            if macd is not None and not macd.empty:
                df['macd'] = macd.iloc[:, 0]
                df['macd_histogram'] = macd.iloc[:, 1]
                df['macd_signal'] = macd.iloc[:, 2]
                # Handle NaN values in MACD comparison
                df['macd_bullish'] = ((df['macd'] > df['macd_signal']) & df['macd'].notna() & df['macd_signal'].notna()).astype(int)
            
            # ADX (Average Directional Index)
            adx = ta.adx(df['high'], df['low'], df['close'], 
                        length=self.indicators_config['adx']['length'])
            if adx is not None and not adx.empty:
                df['adx'] = adx.iloc[:, 0]
                df['dmp'] = adx.iloc[:, 1]  # Directional Movement Positive
                df['dmn'] = adx.iloc[:, 2]  # Directional Movement Negative
                df['adx_strong_trend'] = ((df['adx'] > 25) & df['adx'].notna()).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating composite indicators: {e}")
            return df
    
    def _add_custom_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add custom engineered features."""
        try:
            # Price change features
            df['price_change'] = df['close'].pct_change()
            df['price_change_abs'] = df['price_change'].abs()
            
            # High-Low spread
            df['hl_spread'] = (df['high'] - df['low']) / df['close']
            
            # Open-Close spread
            df['oc_spread'] = (df['close'] - df['open']) / df['open']
            
            # Rolling statistics
            df['price_volatility_5'] = df['close'].rolling(5).std() / df['close'].rolling(5).mean()
            df['price_volatility_20'] = df['close'].rolling(20).std() / df['close'].rolling(20).mean()
            
            # Support/Resistance levels (simplified)
            df['resistance_level'] = df['high'].rolling(20).max()
            df['support_level'] = df['low'].rolling(20).min()
            df['price_near_resistance'] = (df['close'] / df['resistance_level'] > 0.98).astype(int)
            df['price_near_support'] = (df['close'] / df['support_level'] < 1.02).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error calculating custom features: {e}")
            return df
    
    def get_feature_columns(self) -> List[str]:
        """Get list of all technical indicator column names."""
        # This would be populated after running calculate_all_indicators
        base_features = [
            'ema_12', 'ema_26', 'ema_9', 'sma_20', 'sma_50',
            'price_above_ema12', 'price_above_sma20', 'price_above_sma50',
            'ema_cross_signal', 'sma_cross_signal',
            'rsi', 'rsi_overbought', 'rsi_oversold',
            'stoch_k', 'stoch_d', 'stoch_overbought', 'stoch_oversold',
            'williams_r',
            'bb_lower', 'bb_mid', 'bb_upper', 'bb_width', 'bb_position',
            'atr', 'atr_percent',
            'obv', 'ad', 'volume_sma', 'volume_ratio',
            'macd', 'macd_histogram', 'macd_signal', 'macd_bullish',
            'adx', 'dmp', 'dmn', 'adx_strong_trend',
            'price_change', 'price_change_abs', 'hl_spread', 'oc_spread',
            'price_volatility_5', 'price_volatility_20',
            'resistance_level', 'support_level', 'price_near_resistance', 'price_near_support'
        ]
        return base_features
