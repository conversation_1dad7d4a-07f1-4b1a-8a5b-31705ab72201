"""
Feature engineering pipeline that combines technical indicators and sentiment analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from loguru import logger

from .technical_indicators import TechnicalIndicators
from .sentiment_analyzer import SentimentAnalyzer
from ..data_collection import DataAggregator
from ..utils import save_dataframe_to_parquet, load_dataframe_from_parquet


class FeaturePipeline:
    """Complete feature engineering pipeline for the trading system."""
    
    def __init__(self, data_dir: str = "data/processed"):
        self.data_dir = data_dir
        self.technical_indicators = TechnicalIndicators()
        self.sentiment_analyzer = SentimentAnalyzer()
        self.data_aggregator = DataAggregator(data_dir)
        
    def create_features(
        self, 
        market_data: pd.DataFrame, 
        tweets: Optional[List[Dict]] = None,
        symbol: str = "BTC",
        timeframe: str = "1min"
    ) -> pd.DataFrame:
        """
        Create complete feature set from market data and tweets.
        
        Args:
            market_data: OHLCV market data
            tweets: List of tweet dictionaries (optional)
            symbol: Trading symbol
            timeframe: Data timeframe
            
        Returns:
            DataFrame with all features
        """
        try:
            logger.info(f"Creating features for {symbol} - {len(market_data)} market data points")
            
            if market_data.empty:
                logger.warning("Empty market data provided")
                return pd.DataFrame()
            
            # Step 1: Calculate technical indicators
            logger.info("Calculating technical indicators...")
            features_df = self.technical_indicators.calculate_all_indicators(market_data)
            
            # Step 2: Process sentiment data if available
            if tweets and self.sentiment_analyzer.is_model_loaded():
                logger.info(f"Processing sentiment data from {len(tweets)} tweets...")
                
                # Analyze sentiment
                analyzed_tweets = self.sentiment_analyzer.analyze_tweets(tweets)
                
                # Create sentiment features
                sentiment_features = self.sentiment_analyzer.create_sentiment_features(
                    analyzed_tweets, timeframe
                )
                
                if not sentiment_features.empty:
                    # Synchronize with market data
                    features_df = self.data_aggregator.synchronize_data(
                        features_df, sentiment_features, method="forward_fill"
                    )
                    logger.info("Successfully integrated sentiment features")
                else:
                    logger.warning("No sentiment features created")
            else:
                if not tweets:
                    logger.info("No tweets provided, skipping sentiment analysis")
                else:
                    logger.warning("Sentiment analyzer not loaded, skipping sentiment analysis")
            
            # Step 3: Add market regime features
            features_df = self._add_market_regime_features(features_df)
            
            # Step 4: Add time-based features
            features_df = self._add_time_features(features_df)
            
            # Step 5: Clean and validate features
            features_df = self._clean_features(features_df)
            
            logger.info(f"Feature creation completed: {len(features_df)} rows, {len(features_df.columns)} features")
            return features_df
            
        except Exception as e:
            logger.error(f"Error in feature pipeline: {e}")
            return market_data.copy()
    
    def _add_market_regime_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add features that help identify market regimes."""
        try:
            # Volatility regime indicators
            if 'atr_percent' in df.columns:
                df['volatility_regime'] = pd.cut(
                    df['atr_percent'], 
                    bins=[0, 1, 3, 100], 
                    labels=['low', 'medium', 'high']
                )
                df['volatility_regime_low'] = (df['volatility_regime'] == 'low').astype(int)
                df['volatility_regime_medium'] = (df['volatility_regime'] == 'medium').astype(int)
                df['volatility_regime_high'] = (df['volatility_regime'] == 'high').astype(int)
            
            # Trend strength indicators
            trend_indicators = ['ema_cross_signal', 'sma_cross_signal', 'macd_bullish']
            available_trend = [col for col in trend_indicators if col in df.columns]
            
            if available_trend:
                df['trend_strength'] = df[available_trend].sum(axis=1)
                df['strong_uptrend'] = (df['trend_strength'] >= 2).astype(int)
                df['strong_downtrend'] = (df['trend_strength'] <= -2).astype(int)
            
            # Market state combinations
            if all(col in df.columns for col in ['rsi', 'bb_position', 'adx']):
                # Overbought/oversold with trend strength
                df['overbought_strong_trend'] = (
                    (df['rsi'] > 70) & (df['adx'] > 25)
                ).astype(int)
                df['oversold_strong_trend'] = (
                    (df['rsi'] < 30) & (df['adx'] > 25)
                ).astype(int)
                
                # Bollinger Band squeeze (low volatility)
                df['bb_squeeze'] = (df['bb_width'] < df['bb_width'].rolling(20).quantile(0.2)).astype(int)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding market regime features: {e}")
            return df
    
    def _add_time_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Add time-based features."""
        try:
            if not isinstance(df.index, pd.DatetimeIndex):
                logger.warning("DataFrame index is not datetime, skipping time features")
                return df
            
            # Hour of day (market hours matter in crypto)
            df['hour'] = df.index.hour
            df['is_us_market_hours'] = ((df['hour'] >= 9) & (df['hour'] <= 16)).astype(int)
            df['is_asian_market_hours'] = ((df['hour'] >= 21) | (df['hour'] <= 5)).astype(int)
            
            # Day of week
            df['day_of_week'] = df.index.dayofweek
            df['is_weekend'] = (df['day_of_week'] >= 5).astype(int)
            
            # Cyclical encoding for time features
            df['hour_sin'] = np.sin(2 * np.pi * df['hour'] / 24)
            df['hour_cos'] = np.cos(2 * np.pi * df['hour'] / 24)
            df['day_sin'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
            df['day_cos'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
            
            return df
            
        except Exception as e:
            logger.error(f"Error adding time features: {e}")
            return df
    
    def _clean_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean and validate features."""
        try:
            # Remove infinite values
            df = df.replace([np.inf, -np.inf], np.nan)
            
            # Forward fill missing values for technical indicators
            technical_cols = self.technical_indicators.get_feature_columns()
            available_technical = [col for col in technical_cols if col in df.columns]

            if available_technical:
                df[available_technical] = df[available_technical].ffill()

            # Convert categorical columns to numeric before cleaning
            categorical_cols = df.select_dtypes(include=['category']).columns
            for col in categorical_cols:
                # Convert categorical to numeric codes
                df[col] = df[col].cat.codes
                # Replace -1 (NaN category code) with 0
                df[col] = df[col].replace(-1, 0)

            # Fill remaining NaN values with 0
            df = df.fillna(0)

            # Remove columns with all zeros (no information)
            zero_cols = df.columns[(df == 0).all()].tolist()
            if zero_cols:
                logger.info(f"Removing zero-variance columns: {zero_cols}")
                df = df.drop(columns=zero_cols)
            
            # Ensure no duplicate columns
            df = df.loc[:, ~df.columns.duplicated()]
            
            return df
            
        except Exception as e:
            logger.error(f"Error cleaning features: {e}")
            return df
    
    def get_feature_importance_groups(self) -> Dict[str, List[str]]:
        """Get feature groups for analysis and model interpretation."""
        return {
            'price_action': ['open', 'high', 'low', 'close', 'volume'],
            'trend': ['ema_12', 'ema_26', 'sma_20', 'sma_50', 'ema_cross_signal', 'sma_cross_signal'],
            'momentum': ['rsi', 'stoch_k', 'stoch_d', 'williams_r', 'macd', 'macd_histogram'],
            'volatility': ['atr', 'atr_percent', 'bb_width', 'bb_position', 'price_volatility_5'],
            'volume': ['obv', 'ad', 'volume_ratio'],
            'sentiment': [col for col in [] if col.startswith('sentiment_')],  # Will be populated dynamically
            'regime': ['volatility_regime_low', 'volatility_regime_medium', 'volatility_regime_high',
                      'strong_uptrend', 'strong_downtrend', 'bb_squeeze'],
            'time': ['hour', 'day_of_week', 'is_weekend', 'hour_sin', 'hour_cos', 'day_sin', 'day_cos']
        }
    
    def save_features(self, features_df: pd.DataFrame, symbol: str, timeframe: str = "1min") -> None:
        """Save features to file."""
        try:
            filename = f"{symbol}_{timeframe}_features.parquet"
            filepath = f"{self.data_dir}/{filename}"
            save_dataframe_to_parquet(features_df, filepath)
            
        except Exception as e:
            logger.error(f"Error saving features: {e}")
    
    def load_features(self, symbol: str, timeframe: str = "1min") -> Optional[pd.DataFrame]:
        """Load features from file."""
        try:
            filename = f"{symbol}_{timeframe}_features.parquet"
            filepath = f"{self.data_dir}/{filename}"
            return load_dataframe_from_parquet(filepath)
            
        except Exception as e:
            logger.error(f"Error loading features: {e}")
            return None
    
    def get_model_ready_features(
        self, 
        features_df: pd.DataFrame, 
        target_column: Optional[str] = None
    ) -> Tuple[pd.DataFrame, Optional[pd.Series]]:
        """
        Prepare features for machine learning models.
        
        Args:
            features_df: DataFrame with all features
            target_column: Name of target column (if available)
            
        Returns:
            Tuple of (features_matrix, target_series)
        """
        try:
            # Remove non-numeric columns and OHLCV data (keep only engineered features)
            exclude_cols = ['open', 'high', 'low', 'close', 'volume']
            if target_column:
                exclude_cols.append(target_column)
            
            # Select numeric columns only
            numeric_cols = features_df.select_dtypes(include=[np.number]).columns
            feature_cols = [col for col in numeric_cols if col not in exclude_cols]
            
            X = features_df[feature_cols].copy()
            y = features_df[target_column].copy() if target_column and target_column in features_df.columns else None
            
            logger.info(f"Prepared {len(feature_cols)} features for modeling")
            return X, y
            
        except Exception as e:
            logger.error(f"Error preparing model features: {e}")
            return features_df.copy(), None
