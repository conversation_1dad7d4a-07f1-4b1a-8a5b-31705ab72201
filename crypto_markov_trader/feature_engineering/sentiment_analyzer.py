"""
Sentiment analysis using Hugging Face Transformers.
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Optional, Tuple
from transformers import AutoTokenizer, AutoModelForSequenceClassification, pipeline
import torch
from loguru import logger

from ..config import settings


class SentimentAnalyzer:
    """Analyze sentiment of tweets using pre-trained transformer models."""
    
    def __init__(self, model_name: Optional[str] = None):
        self.model_name = model_name or settings.sentiment_model
        self.tokenizer = None
        self.model = None
        self.sentiment_pipeline = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self._load_model()
    
    def _load_model(self) -> None:
        """Load the sentiment analysis model."""
        try:
            logger.info(f"Loading sentiment model: {self.model_name}")

            # Load tokenizer and model with safetensors
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            try:
                self.model = AutoModelForSequenceClassification.from_pretrained(
                    self.model_name,
                    use_safetensors=True
                )
                logger.info("Using safetensors model format")
            except Exception:
                logger.warning("Safetensors not available, falling back to rule-based sentiment")
                self.sentiment_pipeline = None
                return

            # Create pipeline for easier inference
            self.sentiment_pipeline = pipeline(
                "sentiment-analysis",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                return_all_scores=True
            )

            logger.info(f"Sentiment model loaded successfully on {self.device}")

        except Exception as e:
            logger.error(f"Error loading sentiment model: {e}")
            logger.info("Falling back to rule-based sentiment analysis")
            self.sentiment_pipeline = None
    
    def analyze_text(self, text: str) -> Dict[str, float]:
        """
        Analyze sentiment of a single text.
        
        Args:
            text: Text to analyze
            
        Returns:
            Dictionary with sentiment scores
        """
        if not self.sentiment_pipeline:
            logger.warning("Sentiment model not loaded")
            return {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
        
        try:
            # Clean and preprocess text
            cleaned_text = self._preprocess_text(text)
            
            if not cleaned_text.strip():
                return {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
            
            # Get sentiment scores
            results = self.sentiment_pipeline(cleaned_text)
            
            # Convert to standardized format
            sentiment_scores = self._parse_sentiment_results(results[0])
            
            return sentiment_scores
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment for text: {e}")
            return {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
    
    def analyze_tweets(self, tweets: List[Dict]) -> List[Dict]:
        """
        Analyze sentiment for a list of tweets.
        
        Args:
            tweets: List of tweet dictionaries
            
        Returns:
            List of tweets with added sentiment scores
        """
        if not tweets:
            return []
        
        try:
            logger.info(f"Analyzing sentiment for {len(tweets)} tweets")
            
            analyzed_tweets = []
            
            for tweet in tweets:
                # Handle different tweet formats
                if isinstance(tweet, dict):
                    text = tweet.get('text', '')
                elif isinstance(tweet, str):
                    text = tweet
                else:
                    continue

                # Analyze sentiment
                sentiment = self.analyze_text(text)
                
                # Add sentiment to tweet data
                if isinstance(tweet, dict):
                    tweet_with_sentiment = tweet.copy()
                    tweet_with_sentiment.update({
                        'sentiment_positive': sentiment['positive'],
                        'sentiment_negative': sentiment['negative'],
                        'sentiment_neutral': sentiment['neutral'],
                        'sentiment_compound': sentiment['compound'],
                        'sentiment_label': self._get_sentiment_label(sentiment)
                    })
                else:
                    tweet_with_sentiment = {
                        'text': text,
                        'sentiment_positive': sentiment['positive'],
                        'sentiment_negative': sentiment['negative'],
                        'sentiment_neutral': sentiment['neutral'],
                        'sentiment_compound': sentiment['compound'],
                        'sentiment_label': self._get_sentiment_label(sentiment)
                    }
                
                analyzed_tweets.append(tweet_with_sentiment)
            
            logger.info(f"Completed sentiment analysis for {len(analyzed_tweets)} tweets")
            return analyzed_tweets
            
        except Exception as e:
            logger.error(f"Error analyzing tweet sentiments: {e}")
            return tweets
    
    def _preprocess_text(self, text: str) -> str:
        """
        Preprocess text for sentiment analysis.
        
        Args:
            text: Raw text
            
        Returns:
            Cleaned text
        """
        try:
            # Remove URLs
            import re
            text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
            
            # Remove user mentions and hashtags (but keep the text)
            text = re.sub(r'@\w+', '', text)
            text = re.sub(r'#(\w+)', r'\1', text)
            
            # Remove extra whitespace
            text = ' '.join(text.split())
            
            # Limit length (transformer models have token limits)
            max_length = 512  # Common transformer limit
            if len(text) > max_length:
                text = text[:max_length]
            
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error preprocessing text: {e}")
            return text
    
    def _parse_sentiment_results(self, results: List[Dict]) -> Dict[str, float]:
        """
        Parse sentiment results from the model into standardized format.
        
        Args:
            results: Raw results from sentiment pipeline
            
        Returns:
            Standardized sentiment scores
        """
        try:
            # Initialize scores
            scores = {"positive": 0.0, "negative": 0.0, "neutral": 0.0}
            
            # Parse results based on model output format
            for result in results:
                label = result['label'].lower()
                score = result['score']
                
                # Map different label formats to standard format
                if 'pos' in label or 'positive' in label:
                    scores['positive'] = score
                elif 'neg' in label or 'negative' in label:
                    scores['negative'] = score
                elif 'neu' in label or 'neutral' in label:
                    scores['neutral'] = score
            
            # Calculate compound score (positive - negative)
            scores['compound'] = scores['positive'] - scores['negative']
            
            return scores
            
        except Exception as e:
            logger.error(f"Error parsing sentiment results: {e}")
            return {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
    
    def _get_sentiment_label(self, sentiment: Dict[str, float]) -> str:
        """
        Get sentiment label based on scores.
        
        Args:
            sentiment: Sentiment scores dictionary
            
        Returns:
            Sentiment label ('positive', 'negative', 'neutral')
        """
        compound = sentiment.get('compound', 0.0)
        
        if compound > 0.1:
            return 'positive'
        elif compound < -0.1:
            return 'negative'
        else:
            return 'neutral'
    
    def aggregate_sentiment_scores(
        self, 
        tweets: List[Dict], 
        weight_by_engagement: bool = True
    ) -> Dict[str, float]:
        """
        Aggregate sentiment scores from multiple tweets.
        
        Args:
            tweets: List of tweets with sentiment scores
            weight_by_engagement: Whether to weight by follower count and engagement
            
        Returns:
            Aggregated sentiment scores
        """
        if not tweets:
            return {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
        
        try:
            total_weight = 0.0
            weighted_scores = {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
            
            for tweet in tweets:
                # Calculate weight
                if weight_by_engagement:
                    followers = tweet.get('followers_count', 1)
                    likes = tweet.get('like_count', 0)
                    retweets = tweet.get('retweet_count', 0)
                    
                    # Weight based on follower count and engagement
                    weight = np.log1p(followers) + np.log1p(likes + retweets + 1)
                else:
                    weight = 1.0
                
                # Add weighted scores
                for key in weighted_scores.keys():
                    score = tweet.get(f'sentiment_{key}', 0.0)
                    weighted_scores[key] += score * weight
                
                total_weight += weight
            
            # Normalize by total weight
            if total_weight > 0:
                for key in weighted_scores.keys():
                    weighted_scores[key] /= total_weight
            
            logger.info(f"Aggregated sentiment from {len(tweets)} tweets")
            return weighted_scores
            
        except Exception as e:
            logger.error(f"Error aggregating sentiment scores: {e}")
            return {"positive": 0.0, "negative": 0.0, "neutral": 0.0, "compound": 0.0}
    
    def create_sentiment_features(
        self, 
        tweets: List[Dict], 
        timeframe: str = "1min"
    ) -> pd.DataFrame:
        """
        Create time-series sentiment features from tweets.
        
        Args:
            tweets: List of tweets with sentiment scores
            timeframe: Time aggregation frame
            
        Returns:
            DataFrame with sentiment features over time
        """
        if not tweets:
            return pd.DataFrame()
        
        try:
            # Convert to DataFrame
            try:
                df = pd.DataFrame(tweets)
            except Exception as e:
                logger.error(f"Error creating DataFrame from tweets: {e}")
                return pd.DataFrame()

            if df.empty:
                logger.warning("Empty DataFrame created from tweets")
                return pd.DataFrame()

            # Ensure timestamp column
            if 'created_at' not in df.columns:
                logger.error("No 'created_at' column found in tweets")
                return pd.DataFrame()
            
            df['timestamp'] = pd.to_datetime(df['created_at'])
            df.set_index('timestamp', inplace=True)
            
            # Resample and aggregate sentiment features
            sentiment_cols = [col for col in df.columns if col.startswith('sentiment_')]
            
            if not sentiment_cols:
                logger.warning("No sentiment columns found")
                return pd.DataFrame()
            
            # Aggregate functions for different metrics
            agg_functions = {}
            for col in sentiment_cols:
                agg_functions[col] = ['mean', 'std', 'count']
            
            # Add engagement metrics
            engagement_cols = ['followers_count', 'like_count', 'retweet_count']
            for col in engagement_cols:
                if col in df.columns:
                    agg_functions[col] = 'sum'
            
            # Resample data
            resampled = df.resample(timeframe).agg(agg_functions)
            
            # Flatten column names
            resampled.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col 
                               for col in resampled.columns]
            
            # Fill missing values
            resampled = resampled.fillna(0)
            
            # Add derived features
            if 'sentiment_compound_mean' in resampled.columns:
                resampled['sentiment_trend'] = resampled['sentiment_compound_mean'].rolling(5).mean()
                resampled['sentiment_volatility'] = resampled['sentiment_compound_mean'].rolling(5).std()
            
            logger.info(f"Created sentiment features: {len(resampled)} time periods, {len(resampled.columns)} features")
            return resampled
            
        except Exception as e:
            logger.error(f"Error creating sentiment features: {e}")
            return pd.DataFrame()
    
    def is_model_loaded(self) -> bool:
        """Check if sentiment model is loaded and ready."""
        return self.sentiment_pipeline is not None
