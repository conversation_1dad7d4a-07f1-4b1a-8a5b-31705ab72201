"""
Utility functions for the crypto trading system.
"""

import os
import pandas as pd
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from loguru import logger

from .config import settings


def setup_logging() -> None:
    """Set up logging configuration using loguru."""
    # Remove default handler
    logger.remove()
    
    # Add console handler
    logger.add(
        sink=lambda msg: print(msg, end=""),
        level=settings.log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Add file handler if log file is specified
    if settings.log_file:
        os.makedirs(os.path.dirname(settings.log_file), exist_ok=True)
        logger.add(
            settings.log_file,
            level=settings.log_level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
            rotation="1 day",
            retention="30 days",
            compression="zip"
        )


def get_current_timestamp() -> int:
    """Get current timestamp in milliseconds."""
    return int(datetime.now(timezone.utc).timestamp() * 1000)


def timestamp_to_datetime(timestamp: int) -> datetime:
    """Convert timestamp in milliseconds to datetime object."""
    return datetime.fromtimestamp(timestamp / 1000, tz=timezone.utc)


def ensure_directory_exists(path: str) -> None:
    """Ensure that a directory exists, creating it if necessary."""
    os.makedirs(path, exist_ok=True)


def save_dataframe_to_parquet(df: pd.DataFrame, filepath: str) -> None:
    """Save DataFrame to Parquet file with proper directory creation."""
    ensure_directory_exists(os.path.dirname(filepath))
    df.to_parquet(filepath, index=False)
    logger.info(f"Saved DataFrame with {len(df)} rows to {filepath}")


def load_dataframe_from_parquet(filepath: str) -> Optional[pd.DataFrame]:
    """Load DataFrame from Parquet file if it exists."""
    if os.path.exists(filepath):
        df = pd.read_parquet(filepath)
        logger.info(f"Loaded DataFrame with {len(df)} rows from {filepath}")
        return df
    else:
        logger.warning(f"File {filepath} does not exist")
        return None


def validate_symbol(symbol: str) -> str:
    """Validate and normalize trading symbol."""
    symbol = symbol.upper().strip()
    if symbol not in settings.trading_symbols:
        logger.warning(f"Symbol {symbol} not in configured trading symbols: {settings.trading_symbols}")
    return symbol


def calculate_position_size(account_balance: float, risk_percentage: float, entry_price: float, stop_loss_price: float) -> float:
    """Calculate position size based on risk management rules."""
    if stop_loss_price <= 0 or entry_price <= 0:
        return 0.0
    
    risk_amount = account_balance * risk_percentage
    price_difference = abs(entry_price - stop_loss_price)
    
    if price_difference == 0:
        return 0.0
    
    position_size = risk_amount / price_difference
    
    # Apply position size limits
    position_size = min(position_size, settings.max_position_size)
    position_size = max(position_size, 0.0)
    
    return position_size
