"""
Hidden Markov Model implementation for market regime identification.
"""

import numpy as np
import pandas as pd
import pickle
import joblib
import os
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from hmmlearn import hmm
from loguru import logger

from ..config import settings
from ..utils import ensure_directory_exists


class MarkovRegimeModel:
    """Hidden Markov Model for identifying market regimes."""
    
    def __init__(
        self, 
        n_components: int = None,
        covariance_type: str = None,
        n_iter: int = 100,
        random_state: int = 42
    ):
        """
        Initialize the HMM model.
        
        Args:
            n_components: Number of hidden states (market regimes)
            covariance_type: Type of covariance parameters
            n_iter: Maximum number of iterations for training
            random_state: Random seed for reproducibility
        """
        self.n_components = n_components or settings.hmm_n_components
        self.covariance_type = covariance_type or settings.hmm_covariance_type
        self.n_iter = n_iter
        self.random_state = random_state
        
        # Initialize model components
        self.model = None
        self.scaler = StandardScaler()
        self.feature_names = []
        self.regime_labels = {}
        self.is_fitted = False
        
        # Training history
        self.training_history = {
            'log_likelihood': [],
            'n_iter_used': 0,
            'converged': False
        }
        
        self._initialize_model()
    
    def _initialize_model(self) -> None:
        """Initialize the HMM model."""
        try:
            self.model = hmm.GaussianHMM(
                n_components=self.n_components,
                covariance_type=self.covariance_type,
                n_iter=self.n_iter,
                random_state=self.random_state,
                verbose=False
            )
            
            # Define regime labels based on number of components
            if self.n_components == 2:
                self.regime_labels = {0: "Bear Market", 1: "Bull Market"}
            elif self.n_components == 3:
                self.regime_labels = {0: "Bear Market", 1: "Sideways Market", 2: "Bull Market"}
            elif self.n_components == 4:
                self.regime_labels = {
                    0: "Bear Market", 1: "Low Volatility", 
                    2: "High Volatility", 3: "Bull Market"
                }
            else:
                self.regime_labels = {i: f"Regime_{i}" for i in range(self.n_components)}
            
            logger.info(f"Initialized HMM with {self.n_components} components: {list(self.regime_labels.values())}")
            
        except Exception as e:
            logger.error(f"Error initializing HMM model: {e}")
            raise
    
    def prepare_features(self, features_df: pd.DataFrame, feature_columns: Optional[List[str]] = None) -> np.ndarray:
        """
        Prepare features for HMM training/prediction.
        
        Args:
            features_df: DataFrame with features
            feature_columns: Specific columns to use (if None, use all numeric)
            
        Returns:
            Scaled feature matrix
        """
        try:
            if features_df.empty:
                logger.error("Empty features DataFrame provided")
                return np.array([])
            
            # Select feature columns
            if feature_columns is None:
                # Use all numeric columns except OHLCV
                exclude_cols = ['open', 'high', 'low', 'close', 'volume']
                numeric_cols = features_df.select_dtypes(include=[np.number]).columns
                feature_columns = [col for col in numeric_cols if col not in exclude_cols]
            
            # Validate feature columns exist
            available_cols = [col for col in feature_columns if col in features_df.columns]
            if not available_cols:
                logger.error("No valid feature columns found")
                return np.array([])
            
            if len(available_cols) != len(feature_columns):
                missing_cols = set(feature_columns) - set(available_cols)
                logger.warning(f"Missing feature columns: {missing_cols}")
            
            # Extract features
            X = features_df[available_cols].copy()
            
            # Handle missing values
            X = X.fillna(method='ffill').fillna(0)
            
            # Remove infinite values
            X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
            
            # Store feature names
            self.feature_names = available_cols
            
            logger.info(f"Prepared {len(available_cols)} features for HMM: {X.shape}")
            return X.values
            
        except Exception as e:
            logger.error(f"Error preparing features: {e}")
            return np.array([])
    
    def fit(self, features_df: pd.DataFrame, feature_columns: Optional[List[str]] = None) -> 'MarkovRegimeModel':
        """
        Train the HMM model on features.
        
        Args:
            features_df: DataFrame with features
            feature_columns: Specific columns to use for training
            
        Returns:
            Self for method chaining
        """
        try:
            logger.info("Starting HMM model training...")
            
            # Prepare features
            X = self.prepare_features(features_df, feature_columns)
            
            if X.size == 0:
                logger.error("No features available for training")
                return self
            
            # Scale features
            X_scaled = self.scaler.fit_transform(X)
            
            # Train model with multiple random initializations to avoid local optima
            best_model = None
            best_score = -np.inf
            n_trials = 5
            
            logger.info(f"Training HMM with {n_trials} random initializations...")
            
            for trial in range(n_trials):
                try:
                    # Create new model for this trial
                    trial_model = hmm.GaussianHMM(
                        n_components=self.n_components,
                        covariance_type=self.covariance_type,
                        n_iter=self.n_iter,
                        random_state=self.random_state + trial,
                        verbose=False
                    )
                    
                    # Fit model
                    trial_model.fit(X_scaled)
                    
                    # Calculate score
                    score = trial_model.score(X_scaled)
                    
                    logger.info(f"Trial {trial + 1}: Log-likelihood = {score:.2f}")
                    
                    # Keep best model
                    if score > best_score:
                        best_score = score
                        best_model = trial_model
                        
                except Exception as e:
                    logger.warning(f"Trial {trial + 1} failed: {e}")
                    continue
            
            if best_model is None:
                logger.error("All training trials failed")
                return self
            
            # Use best model
            self.model = best_model
            self.is_fitted = True
            
            # Store training history
            self.training_history['log_likelihood'].append(best_score)
            self.training_history['n_iter_used'] = self.model.n_iter
            self.training_history['converged'] = self.model.monitor_.converged
            
            logger.info(f"HMM training completed successfully!")
            logger.info(f"Best log-likelihood: {best_score:.2f}")
            logger.info(f"Converged: {self.training_history['converged']}")
            
            # Analyze learned regimes
            self._analyze_regimes(X_scaled, features_df)
            
            return self
            
        except Exception as e:
            logger.error(f"Error training HMM model: {e}")
            return self
    
    def predict(self, features_df: pd.DataFrame) -> np.ndarray:
        """
        Predict market regimes for given features.
        
        Args:
            features_df: DataFrame with features
            
        Returns:
            Array of predicted regime states
        """
        if not self.is_fitted:
            logger.error("Model not fitted. Call fit() first.")
            return np.array([])
        
        try:
            # Prepare features
            X = self.prepare_features(features_df, self.feature_names)
            
            if X.size == 0:
                logger.error("No features available for prediction")
                return np.array([])
            
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            # Predict states
            states = self.model.predict(X_scaled)
            
            logger.info(f"Predicted regimes for {len(states)} time periods")
            return states
            
        except Exception as e:
            logger.error(f"Error predicting regimes: {e}")
            return np.array([])
    
    def predict_proba(self, features_df: pd.DataFrame) -> np.ndarray:
        """
        Predict regime probabilities for given features.
        
        Args:
            features_df: DataFrame with features
            
        Returns:
            Array of regime probabilities
        """
        if not self.is_fitted:
            logger.error("Model not fitted. Call fit() first.")
            return np.array([])
        
        try:
            # Prepare features
            X = self.prepare_features(features_df, self.feature_names)
            
            if X.size == 0:
                logger.error("No features available for prediction")
                return np.array([])
            
            # Scale features
            X_scaled = self.scaler.transform(X)
            
            # Predict probabilities
            probabilities = self.model.predict_proba(X_scaled)
            
            logger.info(f"Predicted regime probabilities for {len(probabilities)} time periods")
            return probabilities
            
        except Exception as e:
            logger.error(f"Error predicting regime probabilities: {e}")
            return np.array([])
    
    def _analyze_regimes(self, X_scaled: np.ndarray, features_df: pd.DataFrame) -> None:
        """Analyze the learned regimes and their characteristics."""
        try:
            # Predict states for training data
            states = self.model.predict(X_scaled)
            
            # Analyze regime characteristics
            logger.info("Regime Analysis:")
            logger.info("-" * 40)
            
            for state in range(self.n_components):
                state_mask = states == state
                state_count = np.sum(state_mask)
                state_percentage = (state_count / len(states)) * 100
                
                logger.info(f"{self.regime_labels[state]} (State {state}):")
                logger.info(f"  Frequency: {state_count} periods ({state_percentage:.1f}%)")
                
                if state_count > 0 and 'close' in features_df.columns:
                    # Calculate returns for this regime
                    regime_data = features_df[state_mask]
                    if len(regime_data) > 1:
                        returns = regime_data['close'].pct_change().dropna()
                        if len(returns) > 0:
                            avg_return = returns.mean() * 100
                            volatility = returns.std() * 100
                            logger.info(f"  Avg Return: {avg_return:.3f}%")
                            logger.info(f"  Volatility: {volatility:.3f}%")
                
                logger.info("")
            
        except Exception as e:
            logger.error(f"Error analyzing regimes: {e}")
    
    def get_regime_summary(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        Get a summary of predicted regimes with timestamps.
        
        Args:
            features_df: DataFrame with features and timestamps
            
        Returns:
            DataFrame with regime predictions and probabilities
        """
        if not self.is_fitted:
            logger.error("Model not fitted. Call fit() first.")
            return pd.DataFrame()
        
        try:
            # Get predictions
            states = self.predict(features_df)
            probabilities = self.predict_proba(features_df)
            
            if len(states) == 0:
                return pd.DataFrame()
            
            # Create summary DataFrame
            summary_data = {
                'regime_state': states,
                'regime_label': [self.regime_labels[state] for state in states]
            }
            
            # Add probability columns
            for i in range(self.n_components):
                summary_data[f'prob_{self.regime_labels[i].lower().replace(" ", "_")}'] = probabilities[:, i]
            
            summary_df = pd.DataFrame(summary_data, index=features_df.index)
            
            return summary_df
            
        except Exception as e:
            logger.error(f"Error creating regime summary: {e}")
            return pd.DataFrame()
    
    def save_model(self, filepath: str) -> None:
        """Save the trained model to file."""
        try:
            ensure_directory_exists(os.path.dirname(filepath))
            
            model_data = {
                'model': self.model,
                'scaler': self.scaler,
                'feature_names': self.feature_names,
                'regime_labels': self.regime_labels,
                'n_components': self.n_components,
                'covariance_type': self.covariance_type,
                'training_history': self.training_history,
                'is_fitted': self.is_fitted
            }
            
            joblib.dump(model_data, filepath)
            logger.info(f"Model saved to {filepath}")
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
    
    def load_model(self, filepath: str) -> 'MarkovRegimeModel':
        """Load a trained model from file."""
        try:
            model_data = joblib.load(filepath)
            
            self.model = model_data['model']
            self.scaler = model_data['scaler']
            self.feature_names = model_data['feature_names']
            self.regime_labels = model_data['regime_labels']
            self.n_components = model_data['n_components']
            self.covariance_type = model_data['covariance_type']
            self.training_history = model_data['training_history']
            self.is_fitted = model_data['is_fitted']
            
            logger.info(f"Model loaded from {filepath}")
            return self
            
        except Exception as e:
            logger.error(f"Error loading model: {e}")
            return self
