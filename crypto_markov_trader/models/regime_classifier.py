"""
Regime classifier that converts HMM predictions into trading signals.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from enum import Enum
from loguru import logger

from .hmm_model import MarkovRegimeModel


class TradingSignal(Enum):
    """Trading signal enumeration."""
    STRONG_BUY = 2
    BUY = 1
    HOLD = 0
    SELL = -1
    STRONG_SELL = -2


class RegimeClassifier:
    """Converts HMM regime predictions into actionable trading signals."""
    
    def __init__(self, hmm_model: MarkovRegimeModel):
        """
        Initialize regime classifier.
        
        Args:
            hmm_model: Trained HMM model
        """
        self.hmm_model = hmm_model
        self.signal_rules = self._initialize_signal_rules()
        
    def _initialize_signal_rules(self) -> Dict[str, Dict]:
        """Initialize trading signal rules based on regime types."""
        if self.hmm_model.n_components == 2:
            # Two-regime model: Bull/Bear
            return {
                "Bear Market": {
                    "signal": TradingSignal.SELL,
                    "confidence_threshold": 0.6,
                    "description": "Bearish market conditions"
                },
                "Bull Market": {
                    "signal": TradingSignal.BUY,
                    "confidence_threshold": 0.6,
                    "description": "Bullish market conditions"
                }
            }
        
        elif self.hmm_model.n_components == 3:
            # Three-regime model: Bear/Sideways/Bull
            return {
                "Bear Market": {
                    "signal": TradingSignal.SELL,
                    "confidence_threshold": 0.5,
                    "description": "Bearish market conditions, consider selling"
                },
                "Sideways Market": {
                    "signal": TradingSignal.HOLD,
                    "confidence_threshold": 0.4,
                    "description": "Neutral market conditions, hold positions"
                },
                "Bull Market": {
                    "signal": TradingSignal.BUY,
                    "confidence_threshold": 0.5,
                    "description": "Bullish market conditions, consider buying"
                }
            }
        
        elif self.hmm_model.n_components == 4:
            # Four-regime model: Bear/Low Vol/High Vol/Bull
            return {
                "Bear Market": {
                    "signal": TradingSignal.STRONG_SELL,
                    "confidence_threshold": 0.4,
                    "description": "Strong bearish conditions"
                },
                "Low Volatility": {
                    "signal": TradingSignal.HOLD,
                    "confidence_threshold": 0.4,
                    "description": "Low volatility, accumulation phase"
                },
                "High Volatility": {
                    "signal": TradingSignal.HOLD,
                    "confidence_threshold": 0.4,
                    "description": "High volatility, wait for clarity"
                },
                "Bull Market": {
                    "signal": TradingSignal.STRONG_BUY,
                    "confidence_threshold": 0.4,
                    "description": "Strong bullish conditions"
                }
            }
        
        else:
            # Generic rules for other configurations
            rules = {}
            for i, label in self.hmm_model.regime_labels.items():
                if "bear" in label.lower():
                    signal = TradingSignal.SELL
                elif "bull" in label.lower():
                    signal = TradingSignal.BUY
                else:
                    signal = TradingSignal.HOLD
                
                rules[label] = {
                    "signal": signal,
                    "confidence_threshold": 0.5,
                    "description": f"Regime {i} conditions"
                }
            
            return rules
    
    def generate_signals(
        self, 
        features_df: pd.DataFrame,
        use_confidence_threshold: bool = True,
        min_regime_duration: int = 3
    ) -> pd.DataFrame:
        """
        Generate trading signals from regime predictions.
        
        Args:
            features_df: DataFrame with features
            use_confidence_threshold: Whether to apply confidence thresholds
            min_regime_duration: Minimum periods a regime must persist
            
        Returns:
            DataFrame with trading signals and regime information
        """
        try:
            if not self.hmm_model.is_fitted:
                logger.error("HMM model not fitted")
                return pd.DataFrame()
            
            # Get regime predictions
            regime_summary = self.hmm_model.get_regime_summary(features_df)
            
            if regime_summary.empty:
                logger.error("No regime predictions available")
                return pd.DataFrame()
            
            # Initialize signals DataFrame
            signals_df = regime_summary.copy()
            
            # Generate base signals
            signals_df['base_signal'] = signals_df['regime_label'].map(
                lambda x: self.signal_rules[x]['signal'].value
            )
            
            # Apply confidence thresholds
            if use_confidence_threshold:
                signals_df = self._apply_confidence_thresholds(signals_df)
            else:
                signals_df['final_signal'] = signals_df['base_signal']
            
            # Apply regime duration filter
            if min_regime_duration > 1:
                signals_df = self._apply_duration_filter(signals_df, min_regime_duration)
            
            # Add signal descriptions
            signals_df['signal_description'] = signals_df['regime_label'].map(
                lambda x: self.signal_rules[x]['description']
            )
            
            # Add signal strength
            signals_df['signal_strength'] = signals_df.apply(
                self._calculate_signal_strength, axis=1
            )
            
            # Add regime change indicators
            signals_df['regime_changed'] = (
                signals_df['regime_state'] != signals_df['regime_state'].shift(1)
            ).astype(int)
            
            signals_df['signal_changed'] = (
                signals_df['final_signal'] != signals_df['final_signal'].shift(1)
            ).astype(int)
            
            logger.info(f"Generated trading signals for {len(signals_df)} periods")
            return signals_df
            
        except Exception as e:
            logger.error(f"Error generating trading signals: {e}")
            return pd.DataFrame()
    
    def _apply_confidence_thresholds(self, signals_df: pd.DataFrame) -> pd.DataFrame:
        """Apply confidence thresholds to signals."""
        try:
            signals_df['final_signal'] = signals_df['base_signal'].copy()
            
            for regime_label, rules in self.signal_rules.items():
                threshold = rules['confidence_threshold']
                prob_col = f'prob_{regime_label.lower().replace(" ", "_")}'
                
                if prob_col in signals_df.columns:
                    # Set signal to HOLD if confidence is below threshold
                    low_confidence_mask = (
                        (signals_df['regime_label'] == regime_label) & 
                        (signals_df[prob_col] < threshold)
                    )
                    signals_df.loc[low_confidence_mask, 'final_signal'] = TradingSignal.HOLD.value
            
            return signals_df
            
        except Exception as e:
            logger.error(f"Error applying confidence thresholds: {e}")
            return signals_df
    
    def _apply_duration_filter(self, signals_df: pd.DataFrame, min_duration: int) -> pd.DataFrame:
        """Apply minimum regime duration filter."""
        try:
            # Create regime groups
            regime_groups = (signals_df['regime_state'] != signals_df['regime_state'].shift(1)).cumsum()
            
            # Calculate group sizes
            group_sizes = regime_groups.value_counts()
            
            # Identify short-duration regimes
            short_regimes = group_sizes[group_sizes < min_duration].index
            
            # Set signals to HOLD for short-duration regimes
            for group_id in short_regimes:
                mask = regime_groups == group_id
                signals_df.loc[mask, 'final_signal'] = TradingSignal.HOLD.value
            
            logger.info(f"Applied duration filter: {len(short_regimes)} short regimes filtered")
            return signals_df
            
        except Exception as e:
            logger.error(f"Error applying duration filter: {e}")
            return signals_df
    
    def _calculate_signal_strength(self, row: pd.Series) -> float:
        """Calculate signal strength based on regime probability."""
        try:
            regime_label = row['regime_label']
            prob_col = f'prob_{regime_label.lower().replace(" ", "_")}'
            
            if prob_col in row.index:
                return row[prob_col]
            else:
                return 0.5  # Default strength
                
        except Exception as e:
            logger.error(f"Error calculating signal strength: {e}")
            return 0.5
    
    def get_current_signal(self, features_df: pd.DataFrame) -> Dict:
        """
        Get the current trading signal for the latest data point.
        
        Args:
            features_df: DataFrame with features (latest point will be used)
            
        Returns:
            Dictionary with current signal information
        """
        try:
            if features_df.empty:
                return {"error": "No data provided"}
            
            # Generate signals for the data
            signals_df = self.generate_signals(features_df)
            
            if signals_df.empty:
                return {"error": "No signals generated"}
            
            # Get latest signal
            latest = signals_df.iloc[-1]
            
            signal_info = {
                "timestamp": latest.name,
                "regime": latest['regime_label'],
                "regime_state": int(latest['regime_state']),
                "signal": int(latest['final_signal']),
                "signal_name": TradingSignal(latest['final_signal']).name,
                "signal_strength": float(latest['signal_strength']),
                "description": latest['signal_description'],
                "regime_changed": bool(latest['regime_changed']),
                "signal_changed": bool(latest['signal_changed'])
            }
            
            # Add regime probabilities
            prob_cols = [col for col in signals_df.columns if col.startswith('prob_')]
            for col in prob_cols:
                regime_name = col.replace('prob_', '').replace('_', ' ').title()
                signal_info[f"prob_{regime_name}"] = float(latest[col])
            
            return signal_info
            
        except Exception as e:
            logger.error(f"Error getting current signal: {e}")
            return {"error": str(e)}
    
    def analyze_signal_performance(
        self, 
        signals_df: pd.DataFrame, 
        price_data: pd.DataFrame,
        forward_periods: int = 5
    ) -> Dict:
        """
        Analyze the performance of generated signals.
        
        Args:
            signals_df: DataFrame with signals
            price_data: DataFrame with price data
            forward_periods: Number of periods to look ahead for returns
            
        Returns:
            Dictionary with performance metrics
        """
        try:
            if signals_df.empty or price_data.empty:
                return {}
            
            # Align data
            common_index = signals_df.index.intersection(price_data.index)
            if len(common_index) == 0:
                logger.warning("No common timestamps between signals and prices")
                return {}
            
            signals_aligned = signals_df.loc[common_index]
            prices_aligned = price_data.loc[common_index]
            
            # Calculate forward returns
            if 'close' not in prices_aligned.columns:
                logger.error("No 'close' column in price data")
                return {}
            
            forward_returns = prices_aligned['close'].pct_change(forward_periods).shift(-forward_periods)
            
            # Analyze performance by signal type
            performance = {}
            
            for signal_value in signals_aligned['final_signal'].unique():
                signal_name = TradingSignal(signal_value).name
                signal_mask = signals_aligned['final_signal'] == signal_value
                
                if signal_mask.sum() == 0:
                    continue
                
                signal_returns = forward_returns[signal_mask].dropna()
                
                if len(signal_returns) > 0:
                    performance[signal_name] = {
                        "count": len(signal_returns),
                        "mean_return": float(signal_returns.mean()),
                        "std_return": float(signal_returns.std()),
                        "win_rate": float((signal_returns > 0).mean()),
                        "best_return": float(signal_returns.max()),
                        "worst_return": float(signal_returns.min())
                    }
            
            logger.info(f"Analyzed signal performance for {len(performance)} signal types")
            return performance
            
        except Exception as e:
            logger.error(f"Error analyzing signal performance: {e}")
            return {}
    
    def get_regime_transitions(self, signals_df: pd.DataFrame) -> pd.DataFrame:
        """
        Analyze regime transitions and their characteristics.
        
        Args:
            signals_df: DataFrame with signals and regime information
            
        Returns:
            DataFrame with regime transition analysis
        """
        try:
            if signals_df.empty:
                return pd.DataFrame()
            
            # Find regime changes
            regime_changes = signals_df[signals_df['regime_changed'] == 1].copy()
            
            if regime_changes.empty:
                return pd.DataFrame()
            
            # Add transition information
            regime_changes['from_regime'] = signals_df['regime_label'].shift(1)[regime_changes.index]
            regime_changes['to_regime'] = regime_changes['regime_label']
            
            # Calculate time since last transition
            regime_changes['periods_since_last'] = regime_changes.index.to_series().diff().dt.total_seconds() / 60
            
            # Add transition type
            regime_changes['transition_type'] = regime_changes.apply(
                lambda row: self._classify_transition(row['from_regime'], row['to_regime']), 
                axis=1
            )
            
            logger.info(f"Found {len(regime_changes)} regime transitions")
            return regime_changes[['from_regime', 'to_regime', 'transition_type', 'periods_since_last']]
            
        except Exception as e:
            logger.error(f"Error analyzing regime transitions: {e}")
            return pd.DataFrame()
    
    def _classify_transition(self, from_regime: str, to_regime: str) -> str:
        """Classify the type of regime transition."""
        if pd.isna(from_regime):
            return "initial"
        
        # Define regime hierarchy (lower = more bearish)
        regime_hierarchy = {
            "Bear Market": 0,
            "Low Volatility": 1,
            "Sideways Market": 1,
            "High Volatility": 2,
            "Bull Market": 3
        }
        
        from_level = regime_hierarchy.get(from_regime, 1)
        to_level = regime_hierarchy.get(to_regime, 1)
        
        if to_level > from_level:
            return "bullish_transition"
        elif to_level < from_level:
            return "bearish_transition"
        else:
            return "neutral_transition"
