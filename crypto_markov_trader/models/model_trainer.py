"""
Model trainer for the complete HMM-based trading system.
"""

import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from sklearn.model_selection import TimeSeriesSplit
from loguru import logger

from .hmm_model import MarkovRegimeModel
from .regime_classifier import RegimeClassifier
from ..feature_engineering import FeaturePipeline
from ..config import settings
from ..utils import ensure_directory_exists


class ModelTrainer:
    """Complete training pipeline for the HMM-based trading system."""
    
    def __init__(self, data_dir: str = "data", models_dir: str = "data/models"):
        self.data_dir = data_dir
        self.models_dir = models_dir
        self.feature_pipeline = FeaturePipeline("data/processed")
        
        # Training configuration
        self.training_config = {
            'test_size': 0.2,
            'validation_size': 0.1,
            'min_training_samples': 1000,
            'feature_selection_threshold': 0.01,
            'cross_validation_folds': 3
        }
        
        ensure_directory_exists(self.models_dir)
    
    def prepare_training_data(
        self, 
        symbol: str, 
        timeframe: str = "1min",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None
    ) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Prepare training data by loading and processing features.
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            start_date: Start date for training data
            end_date: End date for training data
            
        Returns:
            Tuple of (features_df, price_df)
        """
        try:
            logger.info(f"Preparing training data for {symbol} ({timeframe})")
            
            # Load features
            features_df = self.feature_pipeline.load_features(symbol, timeframe)
            
            if features_df is None or features_df.empty:
                logger.error(f"No features found for {symbol}")
                return pd.DataFrame(), pd.DataFrame()
            
            # Filter by date range if specified
            if start_date or end_date:
                if start_date:
                    features_df = features_df[features_df.index >= start_date]
                if end_date:
                    features_df = features_df[features_df.index <= end_date]
            
            # Extract price data for evaluation
            price_columns = ['open', 'high', 'low', 'close', 'volume']
            available_price_cols = [col for col in price_columns if col in features_df.columns]
            
            if not available_price_cols:
                logger.error("No price columns found in features")
                return pd.DataFrame(), pd.DataFrame()
            
            price_df = features_df[available_price_cols].copy()
            
            # Validate data quality
            if len(features_df) < self.training_config['min_training_samples']:
                logger.warning(f"Insufficient training samples: {len(features_df)} < {self.training_config['min_training_samples']}")
            
            logger.info(f"Prepared {len(features_df)} samples with {len(features_df.columns)} features")
            return features_df, price_df
            
        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            return pd.DataFrame(), pd.DataFrame()
    
    def select_features(self, features_df: pd.DataFrame) -> List[str]:
        """
        Select relevant features for HMM training.
        
        Args:
            features_df: DataFrame with all features
            
        Returns:
            List of selected feature names
        """
        try:
            # Get feature groups
            feature_groups = self.feature_pipeline.get_feature_importance_groups()
            
            # Priority order for feature groups
            priority_groups = [
                'trend', 'momentum', 'volatility', 'volume', 
                'sentiment', 'regime', 'time'
            ]
            
            selected_features = []
            
            # Select features by priority
            for group in priority_groups:
                if group in feature_groups:
                    group_features = [f for f in feature_groups[group] if f in features_df.columns]
                    
                    # Filter out features with low variance
                    for feature in group_features:
                        if features_df[feature].var() > self.training_config['feature_selection_threshold']:
                            selected_features.append(feature)
            
            # Remove highly correlated features
            selected_features = self._remove_correlated_features(features_df, selected_features)
            
            logger.info(f"Selected {len(selected_features)} features for training")
            return selected_features
            
        except Exception as e:
            logger.error(f"Error selecting features: {e}")
            return []
    
    def _remove_correlated_features(
        self, 
        features_df: pd.DataFrame, 
        feature_list: List[str],
        correlation_threshold: float = 0.95
    ) -> List[str]:
        """Remove highly correlated features."""
        try:
            if len(feature_list) <= 1:
                return feature_list
            
            # Calculate correlation matrix
            feature_data = features_df[feature_list]
            corr_matrix = feature_data.corr().abs()
            
            # Find highly correlated pairs
            upper_triangle = corr_matrix.where(
                np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
            )
            
            # Find features to drop
            to_drop = [column for column in upper_triangle.columns 
                      if any(upper_triangle[column] > correlation_threshold)]
            
            # Keep features not in drop list
            selected = [f for f in feature_list if f not in to_drop]
            
            if to_drop:
                logger.info(f"Removed {len(to_drop)} highly correlated features")
            
            return selected
            
        except Exception as e:
            logger.error(f"Error removing correlated features: {e}")
            return feature_list
    
    def train_model(
        self, 
        features_df: pd.DataFrame,
        selected_features: List[str],
        n_components: Optional[int] = None,
        cross_validate: bool = True
    ) -> MarkovRegimeModel:
        """
        Train the HMM model with cross-validation.
        
        Args:
            features_df: DataFrame with features
            selected_features: List of features to use
            n_components: Number of HMM components (if None, use config)
            cross_validate: Whether to perform cross-validation
            
        Returns:
            Trained HMM model
        """
        try:
            logger.info("Starting HMM model training...")
            
            if not selected_features:
                logger.error("No features selected for training")
                return None
            
            # Initialize model
            model = MarkovRegimeModel(
                n_components=n_components or settings.hmm_n_components,
                covariance_type=settings.hmm_covariance_type
            )
            
            if cross_validate:
                # Perform time series cross-validation
                cv_scores = self._cross_validate_model(features_df, selected_features, model)
                logger.info(f"Cross-validation scores: {cv_scores}")
            
            # Train final model on all data
            logger.info("Training final model on complete dataset...")
            model.fit(features_df, selected_features)
            
            if not model.is_fitted:
                logger.error("Model training failed")
                return None
            
            logger.info("Model training completed successfully")
            return model
            
        except Exception as e:
            logger.error(f"Error training model: {e}")
            return None
    
    def _cross_validate_model(
        self, 
        features_df: pd.DataFrame, 
        selected_features: List[str],
        model_template: MarkovRegimeModel
    ) -> List[float]:
        """Perform time series cross-validation."""
        try:
            # Use time series split to respect temporal order
            tscv = TimeSeriesSplit(n_splits=self.training_config['cross_validation_folds'])
            
            cv_scores = []
            
            for fold, (train_idx, val_idx) in enumerate(tscv.split(features_df)):
                logger.info(f"Cross-validation fold {fold + 1}")
                
                # Split data
                train_data = features_df.iloc[train_idx]
                val_data = features_df.iloc[val_idx]
                
                # Create new model for this fold
                fold_model = MarkovRegimeModel(
                    n_components=model_template.n_components,
                    covariance_type=model_template.covariance_type,
                    random_state=42 + fold
                )
                
                # Train on fold
                fold_model.fit(train_data, selected_features)
                
                if fold_model.is_fitted:
                    # Evaluate on validation set
                    val_features = fold_model.prepare_features(val_data, selected_features)
                    if val_features.size > 0:
                        val_scaled = fold_model.scaler.transform(val_features)
                        score = fold_model.model.score(val_scaled)
                        cv_scores.append(score)
                        logger.info(f"Fold {fold + 1} score: {score:.2f}")
                    else:
                        logger.warning(f"No validation features for fold {fold + 1}")
                else:
                    logger.warning(f"Fold {fold + 1} training failed")
            
            return cv_scores
            
        except Exception as e:
            logger.error(f"Error in cross-validation: {e}")
            return []
    
    def evaluate_model(
        self, 
        model: MarkovRegimeModel, 
        features_df: pd.DataFrame,
        price_df: pd.DataFrame
    ) -> Dict:
        """
        Evaluate the trained model performance.
        
        Args:
            model: Trained HMM model
            features_df: Features DataFrame
            price_df: Price DataFrame
            
        Returns:
            Dictionary with evaluation metrics
        """
        try:
            logger.info("Evaluating model performance...")
            
            # Create regime classifier
            classifier = RegimeClassifier(model)
            
            # Generate signals
            signals_df = classifier.generate_signals(features_df)
            
            if signals_df.empty:
                logger.error("No signals generated for evaluation")
                return {}
            
            # Analyze signal performance
            performance = classifier.analyze_signal_performance(
                signals_df, price_df, forward_periods=5
            )
            
            # Analyze regime transitions
            transitions = classifier.get_regime_transitions(signals_df)
            
            # Calculate additional metrics
            evaluation_metrics = {
                'model_info': {
                    'n_components': model.n_components,
                    'n_features': len(model.feature_names),
                    'training_samples': len(features_df),
                    'log_likelihood': model.training_history['log_likelihood'][-1] if model.training_history['log_likelihood'] else None,
                    'converged': model.training_history['converged']
                },
                'regime_distribution': signals_df['regime_label'].value_counts().to_dict(),
                'signal_performance': performance,
                'regime_transitions': {
                    'total_transitions': len(transitions),
                    'transition_types': transitions['transition_type'].value_counts().to_dict() if not transitions.empty else {}
                }
            }
            
            logger.info("Model evaluation completed")
            return evaluation_metrics
            
        except Exception as e:
            logger.error(f"Error evaluating model: {e}")
            return {}
    
    def save_trained_model(
        self, 
        model: MarkovRegimeModel, 
        symbol: str, 
        timeframe: str,
        evaluation_metrics: Dict
    ) -> str:
        """
        Save the trained model and its metadata.
        
        Args:
            model: Trained model
            symbol: Trading symbol
            timeframe: Data timeframe
            evaluation_metrics: Model evaluation results
            
        Returns:
            Path to saved model
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_filename = f"{symbol}_{timeframe}_hmm_model_{timestamp}.joblib"
            model_path = os.path.join(self.models_dir, model_filename)
            
            # Save model
            model.save_model(model_path)
            
            # Save metadata
            metadata = {
                'symbol': symbol,
                'timeframe': timeframe,
                'training_date': timestamp,
                'model_path': model_path,
                'feature_names': model.feature_names,
                'regime_labels': model.regime_labels,
                'evaluation_metrics': evaluation_metrics
            }
            
            metadata_path = model_path.replace('.joblib', '_metadata.json')
            import json
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            logger.info(f"Model saved to {model_path}")
            return model_path
            
        except Exception as e:
            logger.error(f"Error saving model: {e}")
            return ""
    
    def train_complete_pipeline(
        self, 
        symbol: str, 
        timeframe: str = "1min",
        n_components: Optional[int] = None
    ) -> Tuple[MarkovRegimeModel, RegimeClassifier, Dict]:
        """
        Train the complete pipeline from data preparation to model evaluation.
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            n_components: Number of HMM components
            
        Returns:
            Tuple of (trained_model, classifier, evaluation_metrics)
        """
        try:
            logger.info(f"Starting complete training pipeline for {symbol}")
            
            # Step 1: Prepare data
            features_df, price_df = self.prepare_training_data(symbol, timeframe)
            
            if features_df.empty:
                logger.error("No training data available")
                return None, None, {}
            
            # Step 2: Select features
            selected_features = self.select_features(features_df)
            
            if not selected_features:
                logger.error("No features selected")
                return None, None, {}
            
            # Step 3: Train model
            model = self.train_model(features_df, selected_features, n_components)
            
            if model is None:
                logger.error("Model training failed")
                return None, None, {}
            
            # Step 4: Create classifier
            classifier = RegimeClassifier(model)
            
            # Step 5: Evaluate model
            evaluation_metrics = self.evaluate_model(model, features_df, price_df)
            
            # Step 6: Save model
            model_path = self.save_trained_model(model, symbol, timeframe, evaluation_metrics)
            
            logger.info("Complete training pipeline finished successfully")
            return model, classifier, evaluation_metrics
            
        except Exception as e:
            logger.error(f"Error in complete training pipeline: {e}")
            return None, None, {}
