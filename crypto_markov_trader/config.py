"""
Configuration management using Pydantic Settings.
"""

from typing import List, Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    # Hyperliquid API Configuration
    hyperliquid_api_url: str = Field(default="https://api.hyperliquid.xyz", description="Hyperliquid API base URL")
    hyperliquid_testnet: bool = Field(default=True, description="Use Hyperliquid testnet")
    hyperliquid_api_key: Optional[str] = Field(default=None, description="Hyperliquid API key")
    hyperliquid_private_key: Optional[str] = Field(default=None, description="Hyperliquid private key")
    
    # Twitter/X API Configuration
    twitter_bearer_token: Optional[str] = Field(default=None, description="Twitter Bearer Token")
    twitter_api_key: Optional[str] = Field(default=None, description="Twitter API Key")
    twitter_api_secret: Optional[str] = Field(default=None, description="Twitter API Secret")
    twitter_access_token: Optional[str] = Field(default=None, description="Twitter Access Token")
    twitter_access_token_secret: Optional[str] = Field(default=None, description="Twitter Access Token Secret")
    
    # MongoDB Configuration
    mongodb_url: str = Field(default="mongodb://localhost:27017", description="MongoDB connection URL")
    mongodb_database: str = Field(default="crypto_trading", description="MongoDB database name")
    
    # Trading Configuration
    trading_symbols: List[str] = Field(default=["BTC", "ETH", "SOL"], description="Trading symbols")
    default_position_size: float = Field(default=100.0, description="Default position size")
    max_position_size: float = Field(default=1000.0, description="Maximum position size")
    risk_percentage: float = Field(default=0.02, description="Risk percentage per trade")
    
    # Model Configuration
    hmm_n_components: int = Field(default=3, description="Number of HMM components (market regimes)")
    hmm_covariance_type: str = Field(default="full", description="HMM covariance type")
    sentiment_model: str = Field(
        default="cardiffnlp/twitter-roberta-base-sentiment-latest",
        description="Hugging Face sentiment analysis model"
    )
    llm_model_name: str = Field(
        default="microsoft/DialoGPT-medium",
        description="LLM model name for trading recommendations"
    )

    # Trading Parameters
    max_leverage: float = Field(default=2.0, description="Maximum portfolio leverage")
    max_drawdown: float = Field(default=0.15, description="Maximum portfolio drawdown")
    max_slippage: float = Field(default=0.001, description="Maximum slippage tolerance")
    order_timeout: int = Field(default=30, description="Order timeout in seconds")
    trading_interval: int = Field(default=60, description="Trading loop interval in seconds")
    min_signal_strength: float = Field(default=0.3, description="Minimum signal strength for trades")
    max_daily_loss: float = Field(default=0.05, description="Maximum daily loss percentage")

    # Data Feed Parameters
    hyperliquid_websocket: bool = Field(default=True, description="Enable Hyperliquid WebSocket")
    hyperliquid_real_time: bool = Field(default=True, description="Enable real-time data")
    data_refresh_interval: int = Field(default=60, description="Data refresh interval in seconds")
    candle_update_interval: int = Field(default=60, description="Candle update interval in seconds")

    # Sentiment Parameters
    twitter_streaming: bool = Field(default=True, description="Enable Twitter streaming")
    sentiment_update_interval: int = Field(default=300, description="Sentiment update interval in seconds")
    sentiment_keywords: List[str] = Field(default=["bitcoin", "BTC", "ethereum", "ETH", "crypto"], description="Sentiment keywords")

    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    log_file: str = Field(default="logs/trading.log", description="Log file path")
    
    # Backtesting Configuration
    backtest_start_date: str = Field(default="2023-01-01", description="Backtesting start date")
    backtest_end_date: str = Field(default="2024-01-01", description="Backtesting end date")
    transaction_cost: float = Field(default=0.001, description="Transaction cost percentage")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


# Global settings instance
settings = Settings()
