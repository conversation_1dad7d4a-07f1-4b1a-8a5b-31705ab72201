"""
Comprehensive backtesting engine for the HMM-based trading strategy.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timed<PERSON>ta
from dataclasses import dataclass
from enum import Enum
from loguru import logger

from ..models import RegimeClassifier, TradingSignal
from ..config import settings


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "buy"
    SELL = "sell"


@dataclass
class Trade:
    """Trade record."""
    timestamp: datetime
    side: OrderSide
    size: float
    price: float
    commission: float
    signal: TradingSignal
    regime: str
    signal_strength: float


@dataclass
class Position:
    """Position record."""
    size: float = 0.0
    entry_price: float = 0.0
    entry_time: Optional[datetime] = None
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0


class BacktestEngine:
    """Comprehensive backtesting engine for strategy evaluation."""
    
    def __init__(
        self,
        initial_capital: float = 10000.0,
        commission_rate: float = 0.001,
        slippage_rate: float = 0.0005,
        max_position_size: float = 1.0,
        risk_per_trade: float = 0.02
    ):
        """
        Initialize backtesting engine.
        
        Args:
            initial_capital: Starting capital
            commission_rate: Commission rate per trade
            slippage_rate: Slippage rate per trade
            max_position_size: Maximum position size as fraction of capital
            risk_per_trade: Risk per trade as fraction of capital
        """
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        self.max_position_size = max_position_size
        self.risk_per_trade = risk_per_trade
        
        # Trading state
        self.capital = initial_capital
        self.position = Position()
        self.trades = []
        self.equity_curve = []
        self.drawdown_curve = []
        
        # Performance tracking
        self.peak_capital = initial_capital
        self.max_drawdown = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
        
    def reset(self) -> None:
        """Reset backtesting state."""
        self.capital = self.initial_capital
        self.position = Position()
        self.trades = []
        self.equity_curve = []
        self.drawdown_curve = []
        self.peak_capital = self.initial_capital
        self.max_drawdown = 0.0
        self.total_trades = 0
        self.winning_trades = 0
        self.losing_trades = 0
    
    def calculate_position_size(
        self, 
        signal: TradingSignal, 
        price: float, 
        signal_strength: float,
        volatility: float = 0.02
    ) -> float:
        """
        Calculate position size based on signal strength and risk management.
        
        Args:
            signal: Trading signal
            price: Current price
            signal_strength: Signal confidence (0-1)
            volatility: Estimated volatility
            
        Returns:
            Position size in base currency
        """
        try:
            # Base position size from risk management
            risk_amount = self.capital * self.risk_per_trade
            
            # Adjust for volatility (higher volatility = smaller position)
            volatility_adjusted_risk = risk_amount / max(volatility, 0.01)
            
            # Adjust for signal strength
            strength_multiplier = signal_strength if signal_strength > 0 else 0.5
            
            # Calculate position size
            if signal in [TradingSignal.STRONG_BUY, TradingSignal.STRONG_SELL]:
                base_size = volatility_adjusted_risk * 1.5  # Larger position for strong signals
            elif signal in [TradingSignal.BUY, TradingSignal.SELL]:
                base_size = volatility_adjusted_risk * 1.0  # Normal position
            else:
                return 0.0  # No position for HOLD
            
            # Apply signal strength multiplier
            position_size = base_size * strength_multiplier
            
            # Apply maximum position size limit
            max_size = self.capital * self.max_position_size
            position_size = min(position_size, max_size)
            
            # Convert to number of units
            units = position_size / price
            
            return max(units, 0.0)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
    
    def execute_trade(
        self,
        timestamp: datetime,
        side: OrderSide,
        size: float,
        price: float,
        signal: TradingSignal,
        regime: str,
        signal_strength: float
    ) -> bool:
        """
        Execute a trade with realistic costs.
        
        Args:
            timestamp: Trade timestamp
            side: Buy or sell
            size: Trade size in units
            price: Trade price
            signal: Trading signal that triggered the trade
            regime: Current market regime
            signal_strength: Signal confidence
            
        Returns:
            True if trade executed successfully
        """
        try:
            if size <= 0:
                return False
            
            # Apply slippage
            if side == OrderSide.BUY:
                execution_price = price * (1 + self.slippage_rate)
            else:
                execution_price = price * (1 - self.slippage_rate)
            
            # Calculate trade value and commission
            trade_value = size * execution_price
            commission = trade_value * self.commission_rate
            
            # Check if we have enough capital
            if side == OrderSide.BUY and (trade_value + commission) > self.capital:
                logger.warning(f"Insufficient capital for trade: {trade_value + commission} > {self.capital}")
                return False
            
            # Execute the trade
            if side == OrderSide.BUY:
                # Close any existing short position first
                if self.position.size < 0:
                    self._close_position(timestamp, execution_price, signal, regime, signal_strength)
                
                # Open/add to long position
                self.position.size += size
                if self.position.entry_time is None:
                    self.position.entry_price = execution_price
                    self.position.entry_time = timestamp
                else:
                    # Average price for additional position
                    total_value = (self.position.size - size) * self.position.entry_price + size * execution_price
                    self.position.entry_price = total_value / self.position.size
                
                self.capital -= (trade_value + commission)
                
            else:  # SELL
                # Close any existing long position first
                if self.position.size > 0:
                    self._close_position(timestamp, execution_price, signal, regime, signal_strength)
                
                # Open/add to short position
                self.position.size -= size
                if self.position.entry_time is None:
                    self.position.entry_price = execution_price
                    self.position.entry_time = timestamp
                else:
                    # Average price for additional position
                    total_value = abs(self.position.size + size) * self.position.entry_price + size * execution_price
                    self.position.entry_price = total_value / abs(self.position.size)
                
                self.capital += (trade_value - commission)
            
            # Record the trade
            trade = Trade(
                timestamp=timestamp,
                side=side,
                size=size,
                price=execution_price,
                commission=commission,
                signal=signal,
                regime=regime,
                signal_strength=signal_strength
            )
            self.trades.append(trade)
            self.total_trades += 1
            
            logger.debug(f"Executed {side.value} trade: {size:.4f} @ {execution_price:.2f}")
            return True
            
        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return False
    
    def _close_position(
        self,
        timestamp: datetime,
        price: float,
        signal: TradingSignal,
        regime: str,
        signal_strength: float
    ) -> None:
        """Close the current position."""
        if self.position.size == 0:
            return
        
        # Calculate P&L
        if self.position.size > 0:  # Long position
            pnl = self.position.size * (price - self.position.entry_price)
            side = OrderSide.SELL
        else:  # Short position
            pnl = abs(self.position.size) * (self.position.entry_price - price)
            side = OrderSide.BUY
        
        # Apply commission
        trade_value = abs(self.position.size) * price
        commission = trade_value * self.commission_rate
        net_pnl = pnl - commission
        
        # Update capital
        self.capital += net_pnl
        self.position.realized_pnl += net_pnl
        
        # Track winning/losing trades
        if net_pnl > 0:
            self.winning_trades += 1
        else:
            self.losing_trades += 1
        
        # Record closing trade
        trade = Trade(
            timestamp=timestamp,
            side=side,
            size=abs(self.position.size),
            price=price,
            commission=commission,
            signal=signal,
            regime=regime,
            signal_strength=signal_strength
        )
        self.trades.append(trade)
        
        # Reset position
        self.position = Position()
    
    def update_unrealized_pnl(self, current_price: float) -> None:
        """Update unrealized P&L for open positions."""
        if self.position.size == 0:
            self.position.unrealized_pnl = 0.0
            return
        
        if self.position.size > 0:  # Long position
            self.position.unrealized_pnl = self.position.size * (current_price - self.position.entry_price)
        else:  # Short position
            self.position.unrealized_pnl = abs(self.position.size) * (self.position.entry_price - current_price)
    
    def get_total_equity(self, current_price: float) -> float:
        """Get total equity including unrealized P&L."""
        self.update_unrealized_pnl(current_price)
        return self.capital + self.position.unrealized_pnl
    
    def update_drawdown(self, current_equity: float) -> None:
        """Update drawdown tracking."""
        if current_equity > self.peak_capital:
            self.peak_capital = current_equity
        
        current_drawdown = (self.peak_capital - current_equity) / self.peak_capital
        self.max_drawdown = max(self.max_drawdown, current_drawdown)
        self.drawdown_curve.append(current_drawdown)
    
    def run_backtest(
        self,
        signals_df: pd.DataFrame,
        price_data: pd.DataFrame,
        regime_classifier: RegimeClassifier
    ) -> Dict[str, Any]:
        """
        Run complete backtest on historical data.
        
        Args:
            signals_df: DataFrame with trading signals
            price_data: DataFrame with OHLCV data
            regime_classifier: Trained regime classifier
            
        Returns:
            Dictionary with backtest results
        """
        try:
            logger.info("Starting backtest...")
            self.reset()
            
            # Align data
            common_index = signals_df.index.intersection(price_data.index)
            if len(common_index) == 0:
                logger.error("No common timestamps between signals and prices")
                return {}
            
            signals_aligned = signals_df.loc[common_index]
            prices_aligned = price_data.loc[common_index]
            
            # Calculate volatility for position sizing
            returns = prices_aligned['close'].pct_change().dropna()
            rolling_volatility = returns.rolling(20).std().fillna(0.02)
            
            # Run backtest
            for timestamp, row in signals_aligned.iterrows():
                if timestamp not in prices_aligned.index:
                    continue
                
                price_row = prices_aligned.loc[timestamp]
                current_price = price_row['close']
                volatility = rolling_volatility.get(timestamp, 0.02)
                
                # Update equity curve
                current_equity = self.get_total_equity(current_price)
                self.equity_curve.append({
                    'timestamp': timestamp,
                    'equity': current_equity,
                    'capital': self.capital,
                    'unrealized_pnl': self.position.unrealized_pnl,
                    'position_size': self.position.size
                })
                
                # Update drawdown
                self.update_drawdown(current_equity)
                
                # Process trading signal
                signal = TradingSignal(row['final_signal'])
                signal_strength = row['signal_strength']
                regime = row['regime_label']
                
                # Calculate position size
                target_size = self.calculate_position_size(
                    signal, current_price, signal_strength, volatility
                )
                
                # Execute trades based on signal
                if signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                    if target_size > 0:
                        self.execute_trade(
                            timestamp, OrderSide.BUY, target_size, current_price,
                            signal, regime, signal_strength
                        )
                
                elif signal in [TradingSignal.SELL, TradingSignal.STRONG_SELL]:
                    if target_size > 0:
                        self.execute_trade(
                            timestamp, OrderSide.SELL, target_size, current_price,
                            signal, regime, signal_strength
                        )
                
                elif signal == TradingSignal.HOLD:
                    # Close position if we have one and signal is HOLD
                    if self.position.size != 0:
                        self._close_position(timestamp, current_price, signal, regime, signal_strength)
            
            # Close any remaining position
            if self.position.size != 0 and len(prices_aligned) > 0:
                final_price = prices_aligned['close'].iloc[-1]
                final_timestamp = prices_aligned.index[-1]
                self._close_position(final_timestamp, final_price, TradingSignal.HOLD, "Final", 1.0)
            
            logger.info(f"Backtest completed: {self.total_trades} trades executed")
            
            # Generate results
            return self._generate_backtest_results(signals_aligned, prices_aligned)
            
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            return {}
    
    def _generate_backtest_results(
        self, 
        signals_df: pd.DataFrame, 
        price_data: pd.DataFrame
    ) -> Dict[str, Any]:
        """Generate comprehensive backtest results."""
        try:
            # Convert equity curve to DataFrame
            equity_df = pd.DataFrame(self.equity_curve)
            if equity_df.empty:
                return {"error": "No equity data generated"}
            
            equity_df.set_index('timestamp', inplace=True)
            
            # Calculate basic metrics
            final_equity = equity_df['equity'].iloc[-1]
            total_return = (final_equity - self.initial_capital) / self.initial_capital
            
            # Calculate returns
            equity_returns = equity_df['equity'].pct_change().dropna()
            
            # Performance metrics
            results = {
                'summary': {
                    'initial_capital': self.initial_capital,
                    'final_equity': final_equity,
                    'total_return': total_return,
                    'total_return_pct': total_return * 100,
                    'max_drawdown': self.max_drawdown,
                    'max_drawdown_pct': self.max_drawdown * 100,
                    'total_trades': self.total_trades,
                    'winning_trades': self.winning_trades,
                    'losing_trades': self.losing_trades,
                    'win_rate': self.winning_trades / max(self.total_trades, 1),
                },
                'equity_curve': equity_df,
                'trades': pd.DataFrame([
                    {
                        'timestamp': trade.timestamp,
                        'side': trade.side.value,
                        'size': trade.size,
                        'price': trade.price,
                        'commission': trade.commission,
                        'signal': trade.signal.name,
                        'regime': trade.regime,
                        'signal_strength': trade.signal_strength
                    }
                    for trade in self.trades
                ])
            }
            
            # Advanced metrics
            if len(equity_returns) > 0:
                results['advanced_metrics'] = self._calculate_advanced_metrics(equity_returns)
            
            return results
            
        except Exception as e:
            logger.error(f"Error generating backtest results: {e}")
            return {"error": str(e)}
    
    def _calculate_advanced_metrics(self, returns: pd.Series) -> Dict[str, float]:
        """Calculate advanced performance metrics."""
        try:
            # Risk-free rate (assume 2% annually)
            risk_free_rate = 0.02 / 252  # Daily risk-free rate
            
            # Sharpe ratio
            excess_returns = returns - risk_free_rate
            sharpe_ratio = excess_returns.mean() / returns.std() * np.sqrt(252) if returns.std() > 0 else 0
            
            # Sortino ratio (downside deviation)
            downside_returns = returns[returns < 0]
            sortino_ratio = excess_returns.mean() / downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 and downside_returns.std() > 0 else 0
            
            # Calmar ratio
            calmar_ratio = (returns.mean() * 252) / self.max_drawdown if self.max_drawdown > 0 else 0
            
            # Maximum consecutive losses
            consecutive_losses = 0
            max_consecutive_losses = 0
            for ret in returns:
                if ret < 0:
                    consecutive_losses += 1
                    max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)
                else:
                    consecutive_losses = 0
            
            return {
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'volatility_annualized': returns.std() * np.sqrt(252),
                'max_consecutive_losses': max_consecutive_losses,
                'profit_factor': self._calculate_profit_factor(),
                'average_trade_return': returns.mean() if len(returns) > 0 else 0,
            }
            
        except Exception as e:
            logger.error(f"Error calculating advanced metrics: {e}")
            return {}
    
    def _calculate_profit_factor(self) -> float:
        """Calculate profit factor (gross profit / gross loss)."""
        try:
            if not self.trades:
                return 0.0
            
            gross_profit = 0.0
            gross_loss = 0.0
            
            for i in range(0, len(self.trades), 2):  # Pairs of trades (open/close)
                if i + 1 < len(self.trades):
                    open_trade = self.trades[i]
                    close_trade = self.trades[i + 1]
                    
                    if open_trade.side == OrderSide.BUY:
                        pnl = close_trade.size * (close_trade.price - open_trade.price)
                    else:
                        pnl = open_trade.size * (open_trade.price - close_trade.price)
                    
                    pnl -= (open_trade.commission + close_trade.commission)
                    
                    if pnl > 0:
                        gross_profit += pnl
                    else:
                        gross_loss += abs(pnl)
            
            return gross_profit / gross_loss if gross_loss > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating profit factor: {e}")
            return 0.0
