"""
Performance metrics and analysis tools for backtesting results.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
from loguru import logger

from ..models import TradingSignal


class PerformanceMetrics:
    """Comprehensive performance analysis for backtesting results."""
    
    def __init__(self):
        self.benchmark_return = 0.0  # Buy and hold return for comparison
        
    def analyze_backtest_results(self, backtest_results: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive analysis of backtest results.
        
        Args:
            backtest_results: Results from BacktestEngine
            
        Returns:
            Dictionary with detailed analysis
        """
        try:
            if 'error' in backtest_results:
                logger.error(f"Cannot analyze results with error: {backtest_results['error']}")
                return {}
            
            summary = backtest_results.get('summary', {})
            equity_curve = backtest_results.get('equity_curve', pd.DataFrame())
            trades_df = backtest_results.get('trades', pd.DataFrame())
            advanced_metrics = backtest_results.get('advanced_metrics', {})
            
            analysis = {
                'performance_summary': self._analyze_performance_summary(summary, advanced_metrics),
                'trade_analysis': self._analyze_trades(trades_df),
                'regime_analysis': self._analyze_regime_performance(trades_df),
                'risk_analysis': self._analyze_risk_metrics(equity_curve, summary),
                'time_analysis': self._analyze_time_patterns(trades_df, equity_curve),
                'drawdown_analysis': self._analyze_drawdowns(equity_curve),
            }
            
            # Calculate benchmark comparison if possible
            if not equity_curve.empty:
                analysis['benchmark_comparison'] = self._compare_to_benchmark(equity_curve)
            
            logger.info("Performance analysis completed")
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing backtest results: {e}")
            return {}
    
    def _analyze_performance_summary(
        self, 
        summary: Dict[str, Any], 
        advanced_metrics: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze overall performance summary."""
        try:
            performance = {
                'returns': {
                    'total_return_pct': summary.get('total_return_pct', 0),
                    'annualized_return': self._annualize_return(summary.get('total_return', 0)),
                    'volatility_annualized': advanced_metrics.get('volatility_annualized', 0),
                    'sharpe_ratio': advanced_metrics.get('sharpe_ratio', 0),
                    'sortino_ratio': advanced_metrics.get('sortino_ratio', 0),
                    'calmar_ratio': advanced_metrics.get('calmar_ratio', 0),
                },
                'risk': {
                    'max_drawdown_pct': summary.get('max_drawdown_pct', 0),
                    'max_consecutive_losses': advanced_metrics.get('max_consecutive_losses', 0),
                    'profit_factor': advanced_metrics.get('profit_factor', 0),
                },
                'trading': {
                    'total_trades': summary.get('total_trades', 0),
                    'win_rate': summary.get('win_rate', 0),
                    'winning_trades': summary.get('winning_trades', 0),
                    'losing_trades': summary.get('losing_trades', 0),
                    'average_trade_return': advanced_metrics.get('average_trade_return', 0),
                }
            }
            
            # Performance rating
            performance['rating'] = self._calculate_performance_rating(performance)
            
            return performance
            
        except Exception as e:
            logger.error(f"Error analyzing performance summary: {e}")
            return {}
    
    def _analyze_trades(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze individual trades."""
        try:
            if trades_df.empty:
                return {'error': 'No trades to analyze'}
            
            # Convert timestamp to datetime if needed
            if 'timestamp' in trades_df.columns:
                trades_df['timestamp'] = pd.to_datetime(trades_df['timestamp'])
            
            analysis = {
                'trade_distribution': {
                    'by_side': trades_df['side'].value_counts().to_dict(),
                    'by_signal': trades_df['signal'].value_counts().to_dict(),
                    'by_regime': trades_df['regime'].value_counts().to_dict(),
                },
                'size_analysis': {
                    'average_trade_size': trades_df['size'].mean(),
                    'median_trade_size': trades_df['size'].median(),
                    'max_trade_size': trades_df['size'].max(),
                    'min_trade_size': trades_df['size'].min(),
                },
                'commission_analysis': {
                    'total_commissions': trades_df['commission'].sum(),
                    'average_commission': trades_df['commission'].mean(),
                    'commission_as_pct_of_trade': (trades_df['commission'] / (trades_df['size'] * trades_df['price'])).mean(),
                },
                'signal_strength_analysis': {
                    'average_signal_strength': trades_df['signal_strength'].mean(),
                    'signal_strength_by_signal': trades_df.groupby('signal')['signal_strength'].mean().to_dict(),
                }
            }
            
            # Time-based analysis
            if 'timestamp' in trades_df.columns:
                trades_df['hour'] = trades_df['timestamp'].dt.hour
                trades_df['day_of_week'] = trades_df['timestamp'].dt.day_name()
                
                analysis['time_patterns'] = {
                    'trades_by_hour': trades_df['hour'].value_counts().sort_index().to_dict(),
                    'trades_by_day': trades_df['day_of_week'].value_counts().to_dict(),
                }
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing trades: {e}")
            return {'error': str(e)}
    
    def _analyze_regime_performance(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze performance by market regime."""
        try:
            if trades_df.empty or 'regime' not in trades_df.columns:
                return {'error': 'No regime data to analyze'}
            
            regime_analysis = {}
            
            for regime in trades_df['regime'].unique():
                regime_trades = trades_df[trades_df['regime'] == regime]
                
                regime_analysis[regime] = {
                    'trade_count': len(regime_trades),
                    'average_size': regime_trades['size'].mean(),
                    'total_commission': regime_trades['commission'].sum(),
                    'signal_distribution': regime_trades['signal'].value_counts().to_dict(),
                    'average_signal_strength': regime_trades['signal_strength'].mean(),
                }
            
            return regime_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing regime performance: {e}")
            return {'error': str(e)}
    
    def _analyze_risk_metrics(
        self, 
        equity_curve: pd.DataFrame, 
        summary: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze risk-related metrics."""
        try:
            if equity_curve.empty:
                return {'error': 'No equity curve data'}
            
            # Calculate Value at Risk (VaR)
            returns = equity_curve['equity'].pct_change().dropna()
            
            risk_metrics = {
                'value_at_risk': {
                    'var_95': np.percentile(returns, 5) if len(returns) > 0 else 0,
                    'var_99': np.percentile(returns, 1) if len(returns) > 0 else 0,
                    'expected_shortfall_95': returns[returns <= np.percentile(returns, 5)].mean() if len(returns) > 0 else 0,
                },
                'drawdown_metrics': {
                    'max_drawdown_pct': summary.get('max_drawdown_pct', 0),
                    'average_drawdown': np.mean([dd for dd in equity_curve.get('drawdown', []) if dd > 0]),
                    'drawdown_duration': self._calculate_drawdown_duration(equity_curve),
                },
                'volatility_metrics': {
                    'daily_volatility': returns.std() if len(returns) > 0 else 0,
                    'annualized_volatility': returns.std() * np.sqrt(252) if len(returns) > 0 else 0,
                    'downside_volatility': returns[returns < 0].std() if len(returns[returns < 0]) > 0 else 0,
                }
            }
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error analyzing risk metrics: {e}")
            return {'error': str(e)}
    
    def _analyze_time_patterns(
        self, 
        trades_df: pd.DataFrame, 
        equity_curve: pd.DataFrame
    ) -> Dict[str, Any]:
        """Analyze time-based patterns in performance."""
        try:
            analysis = {}
            
            # Monthly performance
            if not equity_curve.empty and 'timestamp' in equity_curve.reset_index().columns:
                equity_curve_reset = equity_curve.reset_index()
                equity_curve_reset['month'] = pd.to_datetime(equity_curve_reset['timestamp']).dt.to_period('M')
                monthly_returns = equity_curve_reset.groupby('month')['equity'].last().pct_change().dropna()
                
                analysis['monthly_performance'] = {
                    'best_month': monthly_returns.max(),
                    'worst_month': monthly_returns.min(),
                    'average_monthly_return': monthly_returns.mean(),
                    'monthly_volatility': monthly_returns.std(),
                    'positive_months': (monthly_returns > 0).sum(),
                    'negative_months': (monthly_returns < 0).sum(),
                }
            
            # Weekly patterns
            if not trades_df.empty and 'timestamp' in trades_df.columns:
                trades_df['day_of_week'] = pd.to_datetime(trades_df['timestamp']).dt.day_name()
                analysis['weekly_patterns'] = trades_df['day_of_week'].value_counts().to_dict()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing time patterns: {e}")
            return {'error': str(e)}
    
    def _analyze_drawdowns(self, equity_curve: pd.DataFrame) -> Dict[str, Any]:
        """Analyze drawdown characteristics."""
        try:
            if equity_curve.empty:
                return {'error': 'No equity curve data'}
            
            equity = equity_curve['equity']
            
            # Calculate drawdowns
            peak = equity.expanding().max()
            drawdown = (equity - peak) / peak
            
            # Find drawdown periods
            drawdown_periods = []
            in_drawdown = False
            start_idx = None
            
            for i, dd in enumerate(drawdown):
                if dd < -0.01 and not in_drawdown:  # Start of drawdown (>1%)
                    in_drawdown = True
                    start_idx = i
                elif dd >= -0.001 and in_drawdown:  # End of drawdown
                    in_drawdown = False
                    if start_idx is not None:
                        drawdown_periods.append({
                            'start': equity.index[start_idx],
                            'end': equity.index[i],
                            'duration': i - start_idx,
                            'max_drawdown': drawdown.iloc[start_idx:i+1].min(),
                        })
            
            analysis = {
                'drawdown_summary': {
                    'max_drawdown': drawdown.min(),
                    'average_drawdown': drawdown[drawdown < 0].mean() if len(drawdown[drawdown < 0]) > 0 else 0,
                    'number_of_drawdowns': len(drawdown_periods),
                },
                'drawdown_periods': drawdown_periods[:10],  # Top 10 drawdowns
            }
            
            if drawdown_periods:
                durations = [dd['duration'] for dd in drawdown_periods]
                analysis['drawdown_summary']['average_duration'] = np.mean(durations)
                analysis['drawdown_summary']['max_duration'] = max(durations)
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing drawdowns: {e}")
            return {'error': str(e)}
    
    def _compare_to_benchmark(self, equity_curve: pd.DataFrame) -> Dict[str, Any]:
        """Compare strategy performance to buy-and-hold benchmark."""
        try:
            if equity_curve.empty:
                return {'error': 'No equity curve data'}
            
            # Calculate strategy returns
            strategy_returns = equity_curve['equity'].pct_change().dropna()
            
            # For benchmark, we'd need price data - for now, assume market return
            # In a real implementation, you'd pass in benchmark price data
            benchmark_return = 0.10  # Assume 10% annual market return
            
            comparison = {
                'strategy_total_return': (equity_curve['equity'].iloc[-1] / equity_curve['equity'].iloc[0]) - 1,
                'benchmark_total_return': benchmark_return,
                'excess_return': 0,  # Would calculate if we had benchmark data
                'information_ratio': 0,  # Would calculate if we had benchmark data
                'beta': 0,  # Would calculate if we had benchmark data
                'alpha': 0,  # Would calculate if we had benchmark data
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"Error comparing to benchmark: {e}")
            return {'error': str(e)}
    
    def _annualize_return(self, total_return: float, days: int = 365) -> float:
        """Convert total return to annualized return."""
        if days <= 0:
            return 0
        return (1 + total_return) ** (365 / days) - 1
    
    def _calculate_drawdown_duration(self, equity_curve: pd.DataFrame) -> float:
        """Calculate average drawdown duration."""
        try:
            if equity_curve.empty:
                return 0
            
            equity = equity_curve['equity']
            peak = equity.expanding().max()
            drawdown = (equity - peak) / peak
            
            # Count periods in drawdown
            in_drawdown = drawdown < -0.01  # More than 1% drawdown
            drawdown_periods = in_drawdown.astype(int).diff().fillna(0)
            
            starts = (drawdown_periods == 1).sum()
            total_drawdown_periods = in_drawdown.sum()
            
            return total_drawdown_periods / starts if starts > 0 else 0
            
        except Exception as e:
            logger.error(f"Error calculating drawdown duration: {e}")
            return 0
    
    def _calculate_performance_rating(self, performance: Dict[str, Any]) -> str:
        """Calculate overall performance rating."""
        try:
            score = 0
            
            # Return score (0-30 points)
            annual_return = performance['returns'].get('annualized_return', 0)
            if annual_return > 0.20:
                score += 30
            elif annual_return > 0.15:
                score += 25
            elif annual_return > 0.10:
                score += 20
            elif annual_return > 0.05:
                score += 15
            elif annual_return > 0:
                score += 10
            
            # Sharpe ratio score (0-25 points)
            sharpe = performance['returns'].get('sharpe_ratio', 0)
            if sharpe > 2.0:
                score += 25
            elif sharpe > 1.5:
                score += 20
            elif sharpe > 1.0:
                score += 15
            elif sharpe > 0.5:
                score += 10
            elif sharpe > 0:
                score += 5
            
            # Drawdown score (0-25 points)
            max_dd = performance['risk'].get('max_drawdown_pct', 100)
            if max_dd < 5:
                score += 25
            elif max_dd < 10:
                score += 20
            elif max_dd < 15:
                score += 15
            elif max_dd < 25:
                score += 10
            elif max_dd < 35:
                score += 5
            
            # Win rate score (0-20 points)
            win_rate = performance['trading'].get('win_rate', 0)
            if win_rate > 0.6:
                score += 20
            elif win_rate > 0.55:
                score += 15
            elif win_rate > 0.5:
                score += 10
            elif win_rate > 0.45:
                score += 5
            
            # Rating based on total score
            if score >= 80:
                return "Excellent"
            elif score >= 65:
                return "Good"
            elif score >= 50:
                return "Average"
            elif score >= 35:
                return "Below Average"
            else:
                return "Poor"
                
        except Exception as e:
            logger.error(f"Error calculating performance rating: {e}")
            return "Unknown"
    
    def generate_performance_report(self, analysis: Dict[str, Any]) -> str:
        """Generate a formatted performance report."""
        try:
            report = []
            report.append("=" * 60)
            report.append("PERFORMANCE ANALYSIS REPORT")
            report.append("=" * 60)
            
            # Performance Summary
            if 'performance_summary' in analysis:
                perf = analysis['performance_summary']
                report.append("\n📊 PERFORMANCE SUMMARY")
                report.append("-" * 30)
                
                returns = perf.get('returns', {})
                report.append(f"Total Return: {returns.get('total_return_pct', 0):.2f}%")
                report.append(f"Annualized Return: {returns.get('annualized_return', 0)*100:.2f}%")
                report.append(f"Sharpe Ratio: {returns.get('sharpe_ratio', 0):.2f}")
                report.append(f"Sortino Ratio: {returns.get('sortino_ratio', 0):.2f}")
                
                risk = perf.get('risk', {})
                report.append(f"Max Drawdown: {risk.get('max_drawdown_pct', 0):.2f}%")
                report.append(f"Profit Factor: {risk.get('profit_factor', 0):.2f}")
                
                trading = perf.get('trading', {})
                report.append(f"Total Trades: {trading.get('total_trades', 0)}")
                report.append(f"Win Rate: {trading.get('win_rate', 0)*100:.1f}%")
                
                report.append(f"\n🏆 Overall Rating: {perf.get('rating', 'Unknown')}")
            
            # Trade Analysis
            if 'trade_analysis' in analysis:
                trade_analysis = analysis['trade_analysis']
                if 'error' not in trade_analysis:
                    report.append("\n📈 TRADE ANALYSIS")
                    report.append("-" * 30)
                    
                    dist = trade_analysis.get('trade_distribution', {})
                    if 'by_signal' in dist:
                        report.append("Trades by Signal:")
                        for signal, count in dist['by_signal'].items():
                            report.append(f"  {signal}: {count}")
            
            return "\n".join(report)
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return f"Error generating report: {e}"
