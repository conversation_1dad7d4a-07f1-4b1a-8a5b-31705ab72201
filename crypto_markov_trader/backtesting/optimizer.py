"""
Strategy parameter optimization using various methods.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Callable
from itertools import product
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
from loguru import logger

from .backtest_engine import BacktestEngine
from .metrics import PerformanceMetrics
from ..models import ModelTrainer, RegimeClassifier


class StrategyOptimizer:
    """Optimize strategy parameters using grid search and other methods."""
    
    def __init__(self, max_workers: Optional[int] = None):
        """
        Initialize optimizer.
        
        Args:
            max_workers: Maximum number of parallel workers
        """
        self.max_workers = max_workers or min(mp.cpu_count(), 4)
        self.performance_metrics = PerformanceMetrics()
        
    def optimize_hmm_parameters(
        self,
        features_df: pd.DataFrame,
        price_data: pd.DataFrame,
        parameter_ranges: Dict[str, List],
        optimization_metric: str = 'sharpe_ratio',
        train_test_split: float = 0.7
    ) -> Dict[str, Any]:
        """
        Optimize HMM model parameters using grid search.
        
        Args:
            features_df: Features for training
            price_data: Price data for backtesting
            parameter_ranges: Dictionary of parameter ranges to test
            optimization_metric: Metric to optimize ('sharpe_ratio', 'total_return', etc.)
            train_test_split: Fraction of data for training
            
        Returns:
            Dictionary with optimization results
        """
        try:
            logger.info("Starting HMM parameter optimization...")
            
            # Split data
            split_idx = int(len(features_df) * train_test_split)
            train_features = features_df.iloc[:split_idx]
            test_features = features_df.iloc[split_idx:]
            test_prices = price_data.iloc[split_idx:]
            
            # Generate parameter combinations
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            logger.info(f"Testing {len(param_combinations)} parameter combinations")
            
            # Run optimization
            results = []
            
            if self.max_workers > 1:
                # Parallel optimization
                with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                    futures = []
                    
                    for params in param_combinations:
                        future = executor.submit(
                            self._evaluate_hmm_parameters,
                            params,
                            train_features,
                            test_features,
                            test_prices,
                            optimization_metric
                        )
                        futures.append((future, params))
                    
                    for future, params in futures:
                        try:
                            result = future.result(timeout=300)  # 5 minute timeout
                            if result:
                                result['parameters'] = params
                                results.append(result)
                        except Exception as e:
                            logger.warning(f"Parameter combination failed: {params}, error: {e}")
            else:
                # Sequential optimization
                for params in param_combinations:
                    result = self._evaluate_hmm_parameters(
                        params, train_features, test_features, test_prices, optimization_metric
                    )
                    if result:
                        result['parameters'] = params
                        results.append(result)
            
            if not results:
                logger.error("No successful parameter combinations")
                return {}
            
            # Find best parameters
            best_result = max(results, key=lambda x: x.get(optimization_metric, -np.inf))
            
            # Compile optimization results
            optimization_results = {
                'best_parameters': best_result['parameters'],
                'best_score': best_result.get(optimization_metric, 0),
                'optimization_metric': optimization_metric,
                'total_combinations_tested': len(results),
                'all_results': sorted(results, key=lambda x: x.get(optimization_metric, -np.inf), reverse=True),
                'parameter_sensitivity': self._analyze_parameter_sensitivity(results, parameter_ranges)
            }
            
            logger.info(f"Optimization completed. Best {optimization_metric}: {best_result.get(optimization_metric, 0):.4f}")
            return optimization_results
            
        except Exception as e:
            logger.error(f"Error in parameter optimization: {e}")
            return {}
    
    def optimize_backtest_parameters(
        self,
        signals_df: pd.DataFrame,
        price_data: pd.DataFrame,
        parameter_ranges: Dict[str, List],
        optimization_metric: str = 'sharpe_ratio'
    ) -> Dict[str, Any]:
        """
        Optimize backtesting parameters (position sizing, risk management, etc.).
        
        Args:
            signals_df: Trading signals
            price_data: Price data
            parameter_ranges: Parameter ranges to test
            optimization_metric: Metric to optimize
            
        Returns:
            Optimization results
        """
        try:
            logger.info("Starting backtest parameter optimization...")
            
            # Generate parameter combinations
            param_combinations = self._generate_parameter_combinations(parameter_ranges)
            logger.info(f"Testing {len(param_combinations)} parameter combinations")
            
            results = []
            
            for params in param_combinations:
                try:
                    # Create backtest engine with parameters
                    engine = BacktestEngine(
                        initial_capital=params.get('initial_capital', 10000),
                        commission_rate=params.get('commission_rate', 0.001),
                        slippage_rate=params.get('slippage_rate', 0.0005),
                        max_position_size=params.get('max_position_size', 1.0),
                        risk_per_trade=params.get('risk_per_trade', 0.02)
                    )
                    
                    # Run backtest (need to create a dummy classifier)
                    # This is a simplified version - in practice you'd need the full classifier
                    backtest_results = engine.run_backtest(signals_df, price_data, None)
                    
                    if backtest_results and 'advanced_metrics' in backtest_results:
                        result = {
                            'parameters': params,
                            optimization_metric: backtest_results['advanced_metrics'].get(optimization_metric, 0),
                            'total_return': backtest_results['summary'].get('total_return', 0),
                            'max_drawdown': backtest_results['summary'].get('max_drawdown', 0),
                            'win_rate': backtest_results['summary'].get('win_rate', 0),
                        }
                        results.append(result)
                        
                except Exception as e:
                    logger.warning(f"Backtest failed for parameters {params}: {e}")
                    continue
            
            if not results:
                return {}
            
            # Find best parameters
            best_result = max(results, key=lambda x: x.get(optimization_metric, -np.inf))
            
            return {
                'best_parameters': best_result['parameters'],
                'best_score': best_result.get(optimization_metric, 0),
                'optimization_metric': optimization_metric,
                'all_results': sorted(results, key=lambda x: x.get(optimization_metric, -np.inf), reverse=True)
            }
            
        except Exception as e:
            logger.error(f"Error in backtest parameter optimization: {e}")
            return {}
    
    def _generate_parameter_combinations(self, parameter_ranges: Dict[str, List]) -> List[Dict]:
        """Generate all combinations of parameters."""
        try:
            keys = list(parameter_ranges.keys())
            values = list(parameter_ranges.values())
            
            combinations = []
            for combination in product(*values):
                param_dict = dict(zip(keys, combination))
                combinations.append(param_dict)
            
            return combinations
            
        except Exception as e:
            logger.error(f"Error generating parameter combinations: {e}")
            return []
    
    def _evaluate_hmm_parameters(
        self,
        params: Dict[str, Any],
        train_features: pd.DataFrame,
        test_features: pd.DataFrame,
        test_prices: pd.DataFrame,
        optimization_metric: str
    ) -> Optional[Dict[str, Any]]:
        """Evaluate a single parameter combination."""
        try:
            # Train model with parameters
            trainer = ModelTrainer()
            
            # Select features
            selected_features = trainer.select_features(train_features)
            if not selected_features:
                return None
            
            # Train HMM model
            from ..models import MarkovRegimeModel
            model = MarkovRegimeModel(
                n_components=params.get('n_components', 3),
                covariance_type=params.get('covariance_type', 'full'),
                n_iter=params.get('n_iter', 100)
            )
            
            model.fit(train_features, selected_features)
            
            if not model.is_fitted:
                return None
            
            # Create classifier and generate signals
            classifier = RegimeClassifier(model)
            signals_df = classifier.generate_signals(test_features)
            
            if signals_df.empty:
                return None
            
            # Run backtest
            engine = BacktestEngine(
                initial_capital=params.get('initial_capital', 10000),
                commission_rate=params.get('commission_rate', 0.001),
                risk_per_trade=params.get('risk_per_trade', 0.02)
            )
            
            backtest_results = engine.run_backtest(signals_df, test_prices, classifier)
            
            if not backtest_results or 'advanced_metrics' not in backtest_results:
                return None
            
            # Extract metrics
            advanced_metrics = backtest_results['advanced_metrics']
            summary = backtest_results['summary']
            
            return {
                'sharpe_ratio': advanced_metrics.get('sharpe_ratio', 0),
                'sortino_ratio': advanced_metrics.get('sortino_ratio', 0),
                'calmar_ratio': advanced_metrics.get('calmar_ratio', 0),
                'total_return': summary.get('total_return', 0),
                'max_drawdown': summary.get('max_drawdown', 0),
                'win_rate': summary.get('win_rate', 0),
                'total_trades': summary.get('total_trades', 0),
                'profit_factor': advanced_metrics.get('profit_factor', 0),
            }
            
        except Exception as e:
            logger.error(f"Error evaluating parameters {params}: {e}")
            return None
    
    def _analyze_parameter_sensitivity(
        self,
        results: List[Dict[str, Any]],
        parameter_ranges: Dict[str, List]
    ) -> Dict[str, Any]:
        """Analyze sensitivity of performance to parameter changes."""
        try:
            sensitivity_analysis = {}
            
            for param_name in parameter_ranges.keys():
                param_values = []
                performance_values = []
                
                for result in results:
                    if 'parameters' in result and param_name in result['parameters']:
                        param_values.append(result['parameters'][param_name])
                        performance_values.append(result.get('sharpe_ratio', 0))
                
                if param_values and performance_values:
                    # Calculate correlation between parameter and performance
                    correlation = np.corrcoef(param_values, performance_values)[0, 1]
                    
                    sensitivity_analysis[param_name] = {
                        'correlation_with_performance': correlation,
                        'best_value': param_values[np.argmax(performance_values)],
                        'worst_value': param_values[np.argmin(performance_values)],
                        'value_range': (min(param_values), max(param_values))
                    }
            
            return sensitivity_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing parameter sensitivity: {e}")
            return {}
    
    def walk_forward_optimization(
        self,
        features_df: pd.DataFrame,
        price_data: pd.DataFrame,
        parameter_ranges: Dict[str, List],
        window_size: int = 1000,
        step_size: int = 100,
        optimization_metric: str = 'sharpe_ratio'
    ) -> Dict[str, Any]:
        """
        Perform walk-forward optimization to test parameter stability.
        
        Args:
            features_df: Features data
            price_data: Price data
            parameter_ranges: Parameter ranges to test
            window_size: Size of training window
            step_size: Step size for walk-forward
            optimization_metric: Metric to optimize
            
        Returns:
            Walk-forward optimization results
        """
        try:
            logger.info("Starting walk-forward optimization...")
            
            results = []
            
            # Walk forward through the data
            for start_idx in range(0, len(features_df) - window_size, step_size):
                end_idx = start_idx + window_size
                test_start = end_idx
                test_end = min(test_start + step_size, len(features_df))
                
                if test_end <= test_start:
                    break
                
                logger.info(f"Walk-forward period: {start_idx}-{end_idx} (train), {test_start}-{test_end} (test)")
                
                # Split data for this period
                train_features = features_df.iloc[start_idx:end_idx]
                test_features = features_df.iloc[test_start:test_end]
                test_prices = price_data.iloc[test_start:test_end]
                
                # Optimize parameters for this period
                period_results = self.optimize_hmm_parameters(
                    pd.concat([train_features, test_features]),
                    pd.concat([price_data.iloc[start_idx:end_idx], test_prices]),
                    parameter_ranges,
                    optimization_metric,
                    train_test_split=len(train_features) / (len(train_features) + len(test_features))
                )
                
                if period_results and 'best_parameters' in period_results:
                    results.append({
                        'period': f"{start_idx}-{test_end}",
                        'train_start': start_idx,
                        'train_end': end_idx,
                        'test_start': test_start,
                        'test_end': test_end,
                        'best_parameters': period_results['best_parameters'],
                        'best_score': period_results['best_score']
                    })
            
            if not results:
                return {}
            
            # Analyze parameter stability
            stability_analysis = self._analyze_parameter_stability(results, parameter_ranges)
            
            return {
                'walk_forward_results': results,
                'parameter_stability': stability_analysis,
                'average_performance': np.mean([r['best_score'] for r in results]),
                'performance_std': np.std([r['best_score'] for r in results])
            }
            
        except Exception as e:
            logger.error(f"Error in walk-forward optimization: {e}")
            return {}
    
    def _analyze_parameter_stability(
        self,
        walk_forward_results: List[Dict[str, Any]],
        parameter_ranges: Dict[str, List]
    ) -> Dict[str, Any]:
        """Analyze stability of optimal parameters across time periods."""
        try:
            stability_analysis = {}
            
            for param_name in parameter_ranges.keys():
                param_values = []
                
                for result in walk_forward_results:
                    if param_name in result['best_parameters']:
                        param_values.append(result['best_parameters'][param_name])
                
                if param_values:
                    stability_analysis[param_name] = {
                        'most_common_value': max(set(param_values), key=param_values.count),
                        'value_frequency': {val: param_values.count(val) for val in set(param_values)},
                        'stability_score': param_values.count(max(set(param_values), key=param_values.count)) / len(param_values)
                    }
            
            return stability_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing parameter stability: {e}")
            return {}
