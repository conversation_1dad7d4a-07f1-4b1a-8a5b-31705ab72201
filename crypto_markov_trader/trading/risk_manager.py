"""
Risk management system for live trading.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tu<PERSON>
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
from loguru import logger

from .portfolio_manager import PortfolioManager, Position
from ..models import TradingSignal
from ..config import settings


class RiskLevel(Enum):
    """Risk level enumeration."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RiskAlert:
    """Risk alert data structure."""
    timestamp: datetime
    level: RiskLevel
    category: str
    message: str
    symbol: Optional[str] = None
    current_value: Optional[float] = None
    limit_value: Optional[float] = None
    action_required: bool = False


class RiskManager:
    """Comprehensive risk management system."""
    
    def __init__(self, portfolio_manager: PortfolioManager):
        """
        Initialize risk manager.
        
        Args:
            portfolio_manager: Portfolio manager instance
        """
        self.portfolio_manager = portfolio_manager
        self.risk_alerts = []
        self.risk_limits = self._initialize_risk_limits()
        self.circuit_breakers = self._initialize_circuit_breakers()
        
        # Risk monitoring state
        self.monitoring_enabled = True
        self.emergency_stop = False
        self.last_risk_check = datetime.now()
        
        logger.info("Risk manager initialized")
    
    def _initialize_risk_limits(self) -> Dict[str, Any]:
        """Initialize risk limits from configuration."""
        return {
            'max_portfolio_drawdown': settings.max_drawdown,
            'max_daily_loss': settings.max_daily_loss,
            'max_position_size': settings.max_position_size,
            'max_leverage': settings.max_leverage,
            'max_correlation': 0.8,  # Maximum correlation between positions
            'max_sector_exposure': 0.6,  # Maximum exposure to single sector
            'var_limit_95': 0.05,  # 5% VaR limit
            'var_limit_99': 0.03,  # 3% VaR limit
        }
    
    def _initialize_circuit_breakers(self) -> Dict[str, Any]:
        """Initialize circuit breaker thresholds."""
        return {
            'daily_loss_breaker': 0.05,  # 5% daily loss triggers circuit breaker
            'drawdown_breaker': 0.15,    # 15% drawdown triggers circuit breaker
            'volatility_breaker': 0.10,  # 10% volatility triggers circuit breaker
            'correlation_breaker': 0.9,  # 90% correlation triggers circuit breaker
        }
    
    def check_pre_trade_risk(
        self,
        symbol: str,
        side: str,
        quantity: float,
        price: float,
        signal_strength: float
    ) -> Tuple[bool, List[str]]:
        """
        Check risk before executing a trade.
        
        Args:
            symbol: Trading symbol
            side: 'buy' or 'sell'
            quantity: Trade quantity
            price: Trade price
            signal_strength: Signal confidence
            
        Returns:
            Tuple of (approved, risk_warnings)
        """
        try:
            warnings = []
            approved = True
            
            # Check if emergency stop is active
            if self.emergency_stop:
                return False, ["Emergency stop is active"]
            
            # Check position size limits
            trade_value = quantity * price
            portfolio_value = self.portfolio_manager.get_portfolio_value()
            
            if trade_value > portfolio_value * self.risk_limits['max_position_size']:
                approved = False
                warnings.append(f"Trade size ({trade_value:,.0f}) exceeds position limit")
            
            # Check leverage limits
            current_positions_value = sum(abs(pos.market_value) for pos in self.portfolio_manager.positions.values())
            new_positions_value = current_positions_value + trade_value
            new_leverage = new_positions_value / portfolio_value if portfolio_value > 0 else 0
            
            if new_leverage > self.risk_limits['max_leverage']:
                approved = False
                warnings.append(f"Trade would exceed leverage limit ({new_leverage:.1f}x > {self.risk_limits['max_leverage']:.1f}x)")
            
            # Check daily loss limits
            daily_pnl_pct = self.portfolio_manager.daily_pnl / self.portfolio_manager.initial_capital
            if daily_pnl_pct < -self.risk_limits['max_daily_loss']:
                approved = False
                warnings.append(f"Daily loss limit exceeded ({daily_pnl_pct:.1%})")
            
            # Check signal strength
            if signal_strength < settings.min_signal_strength:
                warnings.append(f"Low signal strength ({signal_strength:.1%})")
                if signal_strength < 0.3:
                    approved = False
            
            # Check portfolio concentration
            current_position = self.portfolio_manager.get_position(symbol)
            if current_position:
                current_exposure = abs(current_position.market_value) / portfolio_value
                new_exposure = (abs(current_position.market_value) + trade_value) / portfolio_value
                
                if new_exposure > self.risk_limits['max_position_size']:
                    approved = False
                    warnings.append(f"Position concentration too high for {symbol}")
            
            # Log risk check
            logger.info(f"Pre-trade risk check for {symbol}: {'APPROVED' if approved else 'REJECTED'}")
            if warnings:
                for warning in warnings:
                    logger.warning(f"Risk warning: {warning}")
            
            return approved, warnings
            
        except Exception as e:
            logger.error(f"Error in pre-trade risk check: {e}")
            return False, [f"Risk check error: {e}"]
    
    def monitor_portfolio_risk(self) -> List[RiskAlert]:
        """Monitor ongoing portfolio risk and generate alerts."""
        try:
            alerts = []
            
            if not self.monitoring_enabled:
                return alerts
            
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            
            # Check drawdown risk
            max_drawdown = portfolio_summary.get('max_drawdown', 0)
            if max_drawdown > self.risk_limits['max_portfolio_drawdown']:
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.HIGH,
                    category="Drawdown",
                    message=f"Portfolio drawdown ({max_drawdown:.1%}) exceeds limit ({self.risk_limits['max_portfolio_drawdown']:.1%})",
                    current_value=max_drawdown,
                    limit_value=self.risk_limits['max_portfolio_drawdown'],
                    action_required=True
                )
                alerts.append(alert)
            
            # Check daily loss
            daily_pnl = portfolio_summary.get('daily_pnl', 0)
            daily_pnl_pct = daily_pnl / self.portfolio_manager.initial_capital
            if daily_pnl_pct < -self.risk_limits['max_daily_loss']:
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.CRITICAL,
                    category="Daily Loss",
                    message=f"Daily loss ({daily_pnl_pct:.1%}) exceeds limit ({self.risk_limits['max_daily_loss']:.1%})",
                    current_value=daily_pnl_pct,
                    limit_value=-self.risk_limits['max_daily_loss'],
                    action_required=True
                )
                alerts.append(alert)
            
            # Check leverage
            leverage = portfolio_summary.get('leverage', 0)
            if leverage > self.risk_limits['max_leverage']:
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.HIGH,
                    category="Leverage",
                    message=f"Portfolio leverage ({leverage:.1f}x) exceeds limit ({self.risk_limits['max_leverage']:.1f}x)",
                    current_value=leverage,
                    limit_value=self.risk_limits['max_leverage'],
                    action_required=True
                )
                alerts.append(alert)
            
            # Check individual position risks
            for symbol, position in self.portfolio_manager.positions.items():
                position_alerts = self._check_position_risk(symbol, position, portfolio_summary['total_value'])
                alerts.extend(position_alerts)
            
            # Check correlation risk
            correlation_alerts = self._check_correlation_risk()
            alerts.extend(correlation_alerts)
            
            # Check circuit breakers
            breaker_alerts = self._check_circuit_breakers(portfolio_summary)
            alerts.extend(breaker_alerts)
            
            # Store alerts
            self.risk_alerts.extend(alerts)
            
            # Keep only recent alerts (last 24 hours)
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.risk_alerts = [alert for alert in self.risk_alerts if alert.timestamp >= cutoff_time]
            
            self.last_risk_check = datetime.now()
            
            return alerts
            
        except Exception as e:
            logger.error(f"Error monitoring portfolio risk: {e}")
            return []
    
    def _check_position_risk(self, symbol: str, position: Position, portfolio_value: float) -> List[RiskAlert]:
        """Check risk for individual position."""
        alerts = []
        
        try:
            position_pct = abs(position.market_value) / portfolio_value if portfolio_value > 0 else 0
            
            # Check position size
            if position_pct > self.risk_limits['max_position_size']:
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.MEDIUM,
                    category="Position Size",
                    message=f"{symbol} position ({position_pct:.1%}) exceeds size limit",
                    symbol=symbol,
                    current_value=position_pct,
                    limit_value=self.risk_limits['max_position_size']
                )
                alerts.append(alert)
            
            # Check unrealized loss
            if position.unrealized_pnl < 0:
                loss_pct = abs(position.unrealized_pnl) / portfolio_value
                if loss_pct > 0.02:  # 2% unrealized loss threshold
                    alert = RiskAlert(
                        timestamp=datetime.now(),
                        level=RiskLevel.MEDIUM,
                        category="Unrealized Loss",
                        message=f"{symbol} unrealized loss ({loss_pct:.1%}) is significant",
                        symbol=symbol,
                        current_value=loss_pct
                    )
                    alerts.append(alert)
            
        except Exception as e:
            logger.error(f"Error checking position risk for {symbol}: {e}")
        
        return alerts
    
    def _check_correlation_risk(self) -> List[RiskAlert]:
        """Check correlation risk between positions."""
        alerts = []
        
        try:
            positions = list(self.portfolio_manager.positions.keys())
            
            if len(positions) < 2:
                return alerts
            
            # For demo purposes, simulate correlation check
            # In production, you'd calculate actual correlations from price data
            high_correlation_pairs = []
            
            for i, symbol1 in enumerate(positions):
                for symbol2 in positions[i+1:]:
                    # Simulate correlation (in practice, calculate from returns)
                    correlation = np.random.uniform(0.3, 0.9)
                    
                    if correlation > self.risk_limits['max_correlation']:
                        high_correlation_pairs.append((symbol1, symbol2, correlation))
            
            for symbol1, symbol2, correlation in high_correlation_pairs:
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.MEDIUM,
                    category="Correlation",
                    message=f"High correlation between {symbol1} and {symbol2} ({correlation:.1%})",
                    current_value=correlation,
                    limit_value=self.risk_limits['max_correlation']
                )
                alerts.append(alert)
            
        except Exception as e:
            logger.error(f"Error checking correlation risk: {e}")
        
        return alerts
    
    def _check_circuit_breakers(self, portfolio_summary: Dict[str, Any]) -> List[RiskAlert]:
        """Check circuit breaker conditions."""
        alerts = []
        
        try:
            # Daily loss circuit breaker
            daily_pnl_pct = portfolio_summary.get('daily_pnl', 0) / self.portfolio_manager.initial_capital
            if daily_pnl_pct < -self.circuit_breakers['daily_loss_breaker']:
                self.emergency_stop = True
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.CRITICAL,
                    category="Circuit Breaker",
                    message=f"Daily loss circuit breaker triggered ({daily_pnl_pct:.1%})",
                    current_value=daily_pnl_pct,
                    limit_value=-self.circuit_breakers['daily_loss_breaker'],
                    action_required=True
                )
                alerts.append(alert)
            
            # Drawdown circuit breaker
            max_drawdown = portfolio_summary.get('max_drawdown', 0)
            if max_drawdown > self.circuit_breakers['drawdown_breaker']:
                self.emergency_stop = True
                alert = RiskAlert(
                    timestamp=datetime.now(),
                    level=RiskLevel.CRITICAL,
                    category="Circuit Breaker",
                    message=f"Drawdown circuit breaker triggered ({max_drawdown:.1%})",
                    current_value=max_drawdown,
                    limit_value=self.circuit_breakers['drawdown_breaker'],
                    action_required=True
                )
                alerts.append(alert)
            
        except Exception as e:
            logger.error(f"Error checking circuit breakers: {e}")
        
        return alerts
    
    def calculate_var(self, confidence_level: float = 0.95, time_horizon: int = 1) -> float:
        """
        Calculate Value at Risk (VaR) for the portfolio.
        
        Args:
            confidence_level: Confidence level (0.95 for 95% VaR)
            time_horizon: Time horizon in days
            
        Returns:
            VaR as percentage of portfolio value
        """
        try:
            # Get portfolio history
            if len(self.portfolio_manager.portfolio_history) < 30:
                return 0.0
            
            # Calculate daily returns
            returns = []
            for i in range(1, len(self.portfolio_manager.portfolio_history)):
                prev_value = self.portfolio_manager.portfolio_history[i-1].total_value
                curr_value = self.portfolio_manager.portfolio_history[i].total_value
                
                if prev_value > 0:
                    daily_return = (curr_value - prev_value) / prev_value
                    returns.append(daily_return)
            
            if not returns:
                return 0.0
            
            # Calculate VaR
            returns_array = np.array(returns)
            var_percentile = (1 - confidence_level) * 100
            var = np.percentile(returns_array, var_percentile)
            
            # Adjust for time horizon
            var_adjusted = var * np.sqrt(time_horizon)
            
            return abs(var_adjusted)
            
        except Exception as e:
            logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    def get_risk_score(self) -> float:
        """Calculate overall portfolio risk score (0-100)."""
        try:
            score = 0.0
            
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            
            # Drawdown component (0-30 points)
            max_drawdown = portfolio_summary.get('max_drawdown', 0)
            drawdown_score = min(max_drawdown / 0.2 * 30, 30)  # Max 30 points for 20% drawdown
            score += drawdown_score
            
            # Leverage component (0-25 points)
            leverage = portfolio_summary.get('leverage', 0)
            leverage_score = min(leverage / 3.0 * 25, 25)  # Max 25 points for 3x leverage
            score += leverage_score
            
            # Concentration component (0-25 points)
            if self.portfolio_manager.positions:
                max_position_pct = max(
                    abs(pos.market_value) / portfolio_summary['total_value'] 
                    for pos in self.portfolio_manager.positions.values()
                )
                concentration_score = min(max_position_pct / 0.5 * 25, 25)  # Max 25 points for 50% concentration
                score += concentration_score
            
            # VaR component (0-20 points)
            var_95 = self.calculate_var(0.95)
            var_score = min(var_95 / 0.05 * 20, 20)  # Max 20 points for 5% VaR
            score += var_score
            
            return min(score, 100.0)
            
        except Exception as e:
            logger.error(f"Error calculating risk score: {e}")
            return 50.0  # Default medium risk
    
    def get_risk_report(self) -> Dict[str, Any]:
        """Generate comprehensive risk report."""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            recent_alerts = [alert for alert in self.risk_alerts if alert.timestamp >= datetime.now() - timedelta(hours=24)]
            
            return {
                'timestamp': datetime.now(),
                'risk_score': self.get_risk_score(),
                'emergency_stop': self.emergency_stop,
                'portfolio_summary': portfolio_summary,
                'risk_metrics': {
                    'var_95': self.calculate_var(0.95),
                    'var_99': self.calculate_var(0.99),
                    'max_drawdown': portfolio_summary.get('max_drawdown', 0),
                    'leverage': portfolio_summary.get('leverage', 0),
                    'daily_pnl_pct': portfolio_summary.get('daily_pnl', 0) / self.portfolio_manager.initial_capital
                },
                'risk_limits': self.risk_limits,
                'circuit_breakers': self.circuit_breakers,
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp,
                        'level': alert.level.value,
                        'category': alert.category,
                        'message': alert.message,
                        'symbol': alert.symbol,
                        'action_required': alert.action_required
                    }
                    for alert in recent_alerts
                ],
                'alert_summary': {
                    'total_alerts': len(recent_alerts),
                    'critical_alerts': len([a for a in recent_alerts if a.level == RiskLevel.CRITICAL]),
                    'high_alerts': len([a for a in recent_alerts if a.level == RiskLevel.HIGH]),
                    'medium_alerts': len([a for a in recent_alerts if a.level == RiskLevel.MEDIUM])
                }
            }
            
        except Exception as e:
            logger.error(f"Error generating risk report: {e}")
            return {}
    
    def reset_emergency_stop(self) -> bool:
        """Reset emergency stop (manual override)."""
        try:
            self.emergency_stop = False
            logger.warning("Emergency stop manually reset")
            return True
        except Exception as e:
            logger.error(f"Error resetting emergency stop: {e}")
            return False
    
    def update_risk_limits(self, new_limits: Dict[str, float]) -> None:
        """Update risk limits."""
        try:
            for key, value in new_limits.items():
                if key in self.risk_limits:
                    old_value = self.risk_limits[key]
                    self.risk_limits[key] = value
                    logger.info(f"Risk limit updated: {key} {old_value} -> {value}")
                else:
                    logger.warning(f"Unknown risk limit: {key}")
        except Exception as e:
            logger.error(f"Error updating risk limits: {e}")
