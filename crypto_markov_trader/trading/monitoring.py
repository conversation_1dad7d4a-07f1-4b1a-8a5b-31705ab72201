"""
Trading monitoring and alerting system.
"""

import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json
try:
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False
from loguru import logger

from .live_trader import LiveTrader
from .portfolio_manager import PortfolioManager
from .risk_manager import RiskManager, RiskAlert, RiskLevel
from ..config import settings


class AlertType(Enum):
    """Alert type enumeration."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class MonitoringAlert:
    """Monitoring alert data structure."""
    timestamp: datetime
    type: AlertType
    category: str
    message: str
    data: Optional[Dict[str, Any]] = None
    acknowledged: bool = False


class TradingMonitor:
    """Comprehensive trading monitoring and alerting system."""
    
    def __init__(self, live_trader: LiveTrader):
        """
        Initialize trading monitor.
        
        Args:
            live_trader: Live trader instance to monitor
        """
        self.live_trader = live_trader
        self.alerts = []
        self.monitoring_enabled = True
        self.alert_handlers = []
        
        # Monitoring thresholds
        self.thresholds = {
            'max_daily_loss_pct': 0.05,
            'max_drawdown_pct': 0.15,
            'min_portfolio_value': self.live_trader.initial_capital * 0.8,
            'max_risk_score': 80,
            'max_position_concentration': 0.4,
            'min_signal_strength': 0.3,
            'max_execution_time': 30,  # seconds
            'max_slippage_pct': 0.01
        }
        
        # Performance tracking
        self.performance_history = []
        self.last_performance_check = datetime.now()
        
        # Health check intervals
        self.health_check_interval = timedelta(minutes=5)
        self.performance_check_interval = timedelta(minutes=15)
        self.alert_cleanup_interval = timedelta(hours=24)
        
        logger.info("Trading monitor initialized")
    
    async def start_monitoring(self) -> None:
        """Start the monitoring system."""
        try:
            if not self.monitoring_enabled:
                return
            
            logger.info("🔍 Starting trading monitoring...")
            
            # Start monitoring tasks
            tasks = [
                self._health_monitoring_loop(),
                self._performance_monitoring_loop(),
                self._alert_processing_loop()
            ]
            
            await asyncio.gather(*tasks)
            
        except Exception as e:
            logger.error(f"Error starting monitoring: {e}")
    
    async def stop_monitoring(self) -> None:
        """Stop the monitoring system."""
        try:
            self.monitoring_enabled = False
            logger.info("🛑 Stopping trading monitoring...")
            
        except Exception as e:
            logger.error(f"Error stopping monitoring: {e}")
    
    async def _health_monitoring_loop(self) -> None:
        """Main health monitoring loop."""
        try:
            while self.monitoring_enabled:
                try:
                    await self._check_system_health()
                    await asyncio.sleep(self.health_check_interval.total_seconds())
                    
                except Exception as e:
                    logger.error(f"Error in health monitoring: {e}")
                    await asyncio.sleep(60)  # Wait longer on error
                    
        except Exception as e:
            logger.error(f"Fatal error in health monitoring loop: {e}")
    
    async def _performance_monitoring_loop(self) -> None:
        """Performance monitoring loop."""
        try:
            while self.monitoring_enabled:
                try:
                    await self._check_performance_metrics()
                    await asyncio.sleep(self.performance_check_interval.total_seconds())
                    
                except Exception as e:
                    logger.error(f"Error in performance monitoring: {e}")
                    await asyncio.sleep(60)
                    
        except Exception as e:
            logger.error(f"Fatal error in performance monitoring loop: {e}")
    
    async def _alert_processing_loop(self) -> None:
        """Alert processing and cleanup loop."""
        try:
            while self.monitoring_enabled:
                try:
                    await self._process_pending_alerts()
                    await self._cleanup_old_alerts()
                    await asyncio.sleep(60)  # Check every minute
                    
                except Exception as e:
                    logger.error(f"Error in alert processing: {e}")
                    await asyncio.sleep(60)
                    
        except Exception as e:
            logger.error(f"Fatal error in alert processing loop: {e}")
    
    async def _check_system_health(self) -> None:
        """Check overall system health."""
        try:
            # Check if live trader is running
            if not self.live_trader.is_running:
                await self._create_alert(
                    AlertType.WARNING,
                    "System Health",
                    "Live trader is not running"
                )
            
            # Check emergency stop status
            if self.live_trader.risk_manager.emergency_stop:
                await self._create_alert(
                    AlertType.CRITICAL,
                    "System Health",
                    "Emergency stop is active"
                )
            
            # Check portfolio value
            portfolio_summary = self.live_trader.portfolio_manager.get_portfolio_summary()
            current_value = portfolio_summary.get('total_value', 0)
            
            if current_value < self.thresholds['min_portfolio_value']:
                await self._create_alert(
                    AlertType.ERROR,
                    "Portfolio Health",
                    f"Portfolio value (${current_value:,.2f}) below minimum threshold (${self.thresholds['min_portfolio_value']:,.2f})"
                )
            
            # Check risk score
            risk_score = self.live_trader.risk_manager.get_risk_score()
            if risk_score > self.thresholds['max_risk_score']:
                await self._create_alert(
                    AlertType.WARNING,
                    "Risk Health",
                    f"Risk score ({risk_score:.1f}) exceeds threshold ({self.thresholds['max_risk_score']})"
                )
            
            # Check execution engine health
            execution_stats = self.live_trader.execution_engine.get_execution_stats()
            if execution_stats['failed_orders'] > 0:
                failure_rate = execution_stats['failed_orders'] / max(execution_stats['total_orders'], 1)
                if failure_rate > 0.1:  # 10% failure rate
                    await self._create_alert(
                        AlertType.WARNING,
                        "Execution Health",
                        f"High order failure rate: {failure_rate:.1%}"
                    )
            
        except Exception as e:
            logger.error(f"Error checking system health: {e}")
    
    async def _check_performance_metrics(self) -> None:
        """Check performance metrics and trends."""
        try:
            portfolio_summary = self.live_trader.portfolio_manager.get_portfolio_summary()
            
            # Check daily P&L
            daily_pnl_pct = portfolio_summary.get('daily_pnl', 0) / self.live_trader.initial_capital
            if daily_pnl_pct < -self.thresholds['max_daily_loss_pct']:
                await self._create_alert(
                    AlertType.ERROR,
                    "Performance",
                    f"Daily loss ({daily_pnl_pct:.1%}) exceeds threshold ({self.thresholds['max_daily_loss_pct']:.1%})"
                )
            
            # Check drawdown
            max_drawdown = portfolio_summary.get('max_drawdown', 0)
            if max_drawdown > self.thresholds['max_drawdown_pct']:
                await self._create_alert(
                    AlertType.ERROR,
                    "Performance",
                    f"Drawdown ({max_drawdown:.1%}) exceeds threshold ({self.thresholds['max_drawdown_pct']:.1%})"
                )
            
            # Check position concentration
            positions = self.live_trader.portfolio_manager.get_all_positions()
            if positions:
                total_value = portfolio_summary.get('total_value', 1)
                max_position_pct = max(abs(pos.market_value) / total_value for pos in positions.values())
                
                if max_position_pct > self.thresholds['max_position_concentration']:
                    await self._create_alert(
                        AlertType.WARNING,
                        "Performance",
                        f"High position concentration ({max_position_pct:.1%})"
                    )
            
            # Store performance snapshot
            performance_snapshot = {
                'timestamp': datetime.now(),
                'portfolio_value': portfolio_summary.get('total_value', 0),
                'daily_pnl': portfolio_summary.get('daily_pnl', 0),
                'total_return': portfolio_summary.get('total_return', 0),
                'max_drawdown': max_drawdown,
                'num_positions': portfolio_summary.get('num_positions', 0),
                'risk_score': self.live_trader.risk_manager.get_risk_score()
            }
            
            self.performance_history.append(performance_snapshot)
            
            # Keep only last 1000 snapshots
            if len(self.performance_history) > 1000:
                self.performance_history = self.performance_history[-1000:]
            
            self.last_performance_check = datetime.now()
            
        except Exception as e:
            logger.error(f"Error checking performance metrics: {e}")
    
    async def _create_alert(
        self,
        alert_type: AlertType,
        category: str,
        message: str,
        data: Optional[Dict[str, Any]] = None
    ) -> None:
        """Create a new monitoring alert."""
        try:
            alert = MonitoringAlert(
                timestamp=datetime.now(),
                type=alert_type,
                category=category,
                message=message,
                data=data
            )
            
            self.alerts.append(alert)
            
            # Log alert
            log_level = {
                AlertType.INFO: logger.info,
                AlertType.WARNING: logger.warning,
                AlertType.ERROR: logger.error,
                AlertType.CRITICAL: logger.critical
            }
            
            log_level[alert_type](f"[{category}] {message}")
            
            # Process alert immediately if critical
            if alert_type == AlertType.CRITICAL:
                await self._handle_critical_alert(alert)
            
        except Exception as e:
            logger.error(f"Error creating alert: {e}")
    
    async def _handle_critical_alert(self, alert: MonitoringAlert) -> None:
        """Handle critical alerts that require immediate action."""
        try:
            # Send immediate notifications
            await self._send_alert_notifications(alert)
            
            # Take automated actions based on alert category
            if alert.category == "System Health" and "Emergency stop" in alert.message:
                # Already handled by risk manager
                pass
            elif alert.category == "Performance" and "Daily loss" in alert.message:
                # Consider stopping trading
                logger.critical("Considering emergency stop due to excessive daily loss")
                # await self.live_trader.emergency_stop()
            
        except Exception as e:
            logger.error(f"Error handling critical alert: {e}")
    
    async def _process_pending_alerts(self) -> None:
        """Process pending alerts and send notifications."""
        try:
            unacknowledged_alerts = [alert for alert in self.alerts if not alert.acknowledged]
            
            for alert in unacknowledged_alerts:
                # Send notifications for high-priority alerts
                if alert.type in [AlertType.ERROR, AlertType.CRITICAL]:
                    await self._send_alert_notifications(alert)
                
                # Mark as acknowledged after processing
                alert.acknowledged = True
            
        except Exception as e:
            logger.error(f"Error processing pending alerts: {e}")
    
    async def _send_alert_notifications(self, alert: MonitoringAlert) -> None:
        """Send alert notifications via configured channels."""
        try:
            # Email notification (if configured)
            if hasattr(settings, 'email_alerts_enabled') and settings.email_alerts_enabled:
                await self._send_email_alert(alert)
            
            # Webhook notification (if configured)
            if hasattr(settings, 'webhook_url') and settings.webhook_url:
                await self._send_webhook_alert(alert)
            
            # Custom alert handlers
            for handler in self.alert_handlers:
                try:
                    await handler(alert)
                except Exception as e:
                    logger.error(f"Error in custom alert handler: {e}")
            
        except Exception as e:
            logger.error(f"Error sending alert notifications: {e}")
    
    async def _send_email_alert(self, alert: MonitoringAlert) -> None:
        """Send email alert notification."""
        try:
            if not hasattr(settings, 'smtp_config'):
                return

            smtp_config = settings.smtp_config
            
            msg = MimeMultipart()
            msg['From'] = smtp_config['from_email']
            msg['To'] = smtp_config['to_email']
            msg['Subject'] = f"Trading Alert: {alert.type.value.upper()} - {alert.category}"
            
            body = f"""
Trading Alert Notification

Type: {alert.type.value.upper()}
Category: {alert.category}
Time: {alert.timestamp}
Message: {alert.message}

Additional Data:
{json.dumps(alert.data, indent=2, default=str) if alert.data else 'None'}

---
Crypto Markov Trader Monitoring System
"""
            
            msg.attach(MimeText(body, 'plain'))
            
            server = smtplib.SMTP(smtp_config['smtp_server'], smtp_config['smtp_port'])
            server.starttls()
            server.login(smtp_config['username'], smtp_config['password'])
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email alert sent for {alert.category}")
            
        except Exception as e:
            logger.error(f"Error sending email alert: {e}")
    
    async def _send_webhook_alert(self, alert: MonitoringAlert) -> None:
        """Send webhook alert notification."""
        try:
            import aiohttp
            
            payload = {
                'timestamp': alert.timestamp.isoformat(),
                'type': alert.type.value,
                'category': alert.category,
                'message': alert.message,
                'data': alert.data
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(settings.webhook_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Webhook alert sent for {alert.category}")
                    else:
                        logger.error(f"Webhook alert failed: {response.status}")
            
        except Exception as e:
            logger.error(f"Error sending webhook alert: {e}")
    
    async def _cleanup_old_alerts(self) -> None:
        """Clean up old alerts."""
        try:
            cutoff_time = datetime.now() - self.alert_cleanup_interval
            
            old_count = len(self.alerts)
            self.alerts = [alert for alert in self.alerts if alert.timestamp >= cutoff_time]
            new_count = len(self.alerts)
            
            if old_count > new_count:
                logger.info(f"Cleaned up {old_count - new_count} old alerts")
            
        except Exception as e:
            logger.error(f"Error cleaning up alerts: {e}")
    
    def add_alert_handler(self, handler: Callable[[MonitoringAlert], None]) -> None:
        """Add custom alert handler."""
        self.alert_handlers.append(handler)
        logger.info("Custom alert handler added")
    
    def get_monitoring_dashboard(self) -> Dict[str, Any]:
        """Get monitoring dashboard data."""
        try:
            # Recent alerts summary
            recent_alerts = [alert for alert in self.alerts if alert.timestamp >= datetime.now() - timedelta(hours=24)]
            alert_summary = {
                'total': len(recent_alerts),
                'critical': len([a for a in recent_alerts if a.type == AlertType.CRITICAL]),
                'error': len([a for a in recent_alerts if a.type == AlertType.ERROR]),
                'warning': len([a for a in recent_alerts if a.type == AlertType.WARNING]),
                'info': len([a for a in recent_alerts if a.type == AlertType.INFO])
            }
            
            # System health status
            trader_status = self.live_trader.get_status()
            
            # Performance metrics
            performance_metrics = {}
            if self.performance_history:
                latest_performance = self.performance_history[-1]
                performance_metrics = {
                    'current_value': latest_performance['portfolio_value'],
                    'daily_pnl': latest_performance['daily_pnl'],
                    'total_return': latest_performance['total_return'],
                    'max_drawdown': latest_performance['max_drawdown'],
                    'risk_score': latest_performance['risk_score']
                }
            
            return {
                'timestamp': datetime.now(),
                'monitoring_enabled': self.monitoring_enabled,
                'system_health': {
                    'trader_running': trader_status.get('is_running', False),
                    'trading_enabled': trader_status.get('trading_enabled', False),
                    'emergency_stop': trader_status.get('emergency_stop', False),
                    'risk_score': trader_status.get('risk_score', 0)
                },
                'alert_summary': alert_summary,
                'performance_metrics': performance_metrics,
                'recent_alerts': [
                    {
                        'timestamp': alert.timestamp,
                        'type': alert.type.value,
                        'category': alert.category,
                        'message': alert.message
                    }
                    for alert in recent_alerts[-10:]  # Last 10 alerts
                ],
                'thresholds': self.thresholds
            }
            
        except Exception as e:
            logger.error(f"Error getting monitoring dashboard: {e}")
            return {'error': str(e)}
    
    def update_thresholds(self, new_thresholds: Dict[str, float]) -> None:
        """Update monitoring thresholds."""
        try:
            for key, value in new_thresholds.items():
                if key in self.thresholds:
                    old_value = self.thresholds[key]
                    self.thresholds[key] = value
                    logger.info(f"Threshold updated: {key} {old_value} -> {value}")
                else:
                    logger.warning(f"Unknown threshold: {key}")
        except Exception as e:
            logger.error(f"Error updating thresholds: {e}")
    
    def get_performance_history(self, hours: int = 24) -> List[Dict[str, Any]]:
        """Get performance history for specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            return [
                perf for perf in self.performance_history 
                if perf['timestamp'] >= cutoff_time
            ]
        except Exception as e:
            logger.error(f"Error getting performance history: {e}")
            return []
