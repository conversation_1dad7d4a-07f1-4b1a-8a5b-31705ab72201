"""
Portfolio management and position tracking for live trading.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from loguru import logger

from .execution_engine import Order, OrderStatus
from ..config import settings


@dataclass
class Position:
    """Position data structure."""
    symbol: str
    quantity: float = 0.0
    average_price: float = 0.0
    market_value: float = 0.0
    unrealized_pnl: float = 0.0
    realized_pnl: float = 0.0
    last_update: datetime = field(default_factory=datetime.now)
    
    @property
    def is_long(self) -> bool:
        return self.quantity > 0
    
    @property
    def is_short(self) -> bool:
        return self.quantity < 0
    
    @property
    def is_flat(self) -> bool:
        return abs(self.quantity) < 1e-8


@dataclass
class PortfolioSnapshot:
    """Portfolio snapshot for tracking performance."""
    timestamp: datetime
    total_value: float
    cash_balance: float
    positions_value: float
    unrealized_pnl: float
    realized_pnl: float
    daily_pnl: float
    positions: Dict[str, Position]


class PortfolioManager:
    """Manage portfolio positions, P&L, and risk metrics."""
    
    def __init__(self, initial_capital: float = None):
        """
        Initialize portfolio manager.
        
        Args:
            initial_capital: Starting capital amount
        """
        self.initial_capital = initial_capital or settings.default_position_size * 100
        self.cash_balance = self.initial_capital
        self.positions = {}  # symbol -> Position
        self.portfolio_history = []
        
        # Performance tracking
        self.daily_pnl = 0.0
        self.total_realized_pnl = 0.0
        self.total_unrealized_pnl = 0.0
        self.peak_value = self.initial_capital
        self.max_drawdown = 0.0
        
        # Risk metrics
        self.position_limits = {}  # symbol -> max position size
        self.sector_limits = {}    # sector -> max exposure
        
        logger.info(f"Portfolio initialized with ${self.initial_capital:,.2f}")
    
    def update_position(
        self,
        symbol: str,
        quantity_change: float,
        price: float,
        fees: float = 0.0
    ) -> None:
        """
        Update position based on trade execution.
        
        Args:
            symbol: Trading symbol
            quantity_change: Change in position (positive for buy, negative for sell)
            price: Execution price
            fees: Trading fees
        """
        try:
            if symbol not in self.positions:
                self.positions[symbol] = Position(symbol=symbol)
            
            position = self.positions[symbol]
            old_quantity = position.quantity
            
            # Calculate realized P&L for position reductions
            if (old_quantity > 0 and quantity_change < 0) or (old_quantity < 0 and quantity_change > 0):
                # Closing or reducing position
                close_quantity = min(abs(quantity_change), abs(old_quantity))
                if old_quantity != 0:
                    realized_pnl = close_quantity * (price - position.average_price) * (1 if old_quantity > 0 else -1)
                    position.realized_pnl += realized_pnl
                    self.total_realized_pnl += realized_pnl
                    
                    logger.info(f"Realized P&L for {symbol}: ${realized_pnl:,.2f}")
            
            # Update position quantity
            new_quantity = old_quantity + quantity_change
            
            # Update average price for position increases
            if (old_quantity >= 0 and quantity_change > 0) or (old_quantity <= 0 and quantity_change < 0):
                # Adding to position or opening new position
                if abs(new_quantity) > 1e-8:
                    total_cost = abs(old_quantity) * position.average_price + abs(quantity_change) * price
                    position.average_price = total_cost / abs(new_quantity)
            
            position.quantity = new_quantity
            position.last_update = datetime.now()
            
            # Update cash balance
            cash_change = -quantity_change * price - fees
            self.cash_balance += cash_change
            
            logger.info(f"Position updated: {symbol} - Quantity: {new_quantity:.6f}, Avg Price: ${position.average_price:.2f}")
            
            # Clean up zero positions
            if position.is_flat:
                del self.positions[symbol]
                logger.info(f"Position closed: {symbol}")
            
        except Exception as e:
            logger.error(f"Error updating position for {symbol}: {e}")
    
    def update_market_prices(self, prices: Dict[str, float]) -> None:
        """
        Update market prices and calculate unrealized P&L.
        
        Args:
            prices: Dictionary of symbol -> current price
        """
        try:
            total_unrealized = 0.0
            
            for symbol, position in self.positions.items():
                if symbol in prices:
                    current_price = prices[symbol]
                    position.market_value = position.quantity * current_price
                    
                    # Calculate unrealized P&L
                    if not position.is_flat:
                        position.unrealized_pnl = position.quantity * (current_price - position.average_price)
                        total_unrealized += position.unrealized_pnl
                    
                    position.last_update = datetime.now()
            
            self.total_unrealized_pnl = total_unrealized
            
        except Exception as e:
            logger.error(f"Error updating market prices: {e}")
    
    def get_portfolio_value(self) -> float:
        """Get total portfolio value."""
        positions_value = sum(pos.market_value for pos in self.positions.values())
        return self.cash_balance + positions_value
    
    def get_position(self, symbol: str) -> Optional[Position]:
        """Get position for a specific symbol."""
        return self.positions.get(symbol)
    
    def get_all_positions(self) -> Dict[str, Position]:
        """Get all current positions."""
        return self.positions.copy()
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get comprehensive portfolio summary."""
        try:
            total_value = self.get_portfolio_value()
            positions_value = sum(pos.market_value for pos in self.positions.values())
            
            # Calculate returns
            total_return = (total_value - self.initial_capital) / self.initial_capital
            
            # Update drawdown
            if total_value > self.peak_value:
                self.peak_value = total_value
            
            current_drawdown = (self.peak_value - total_value) / self.peak_value
            self.max_drawdown = max(self.max_drawdown, current_drawdown)
            
            return {
                'timestamp': datetime.now(),
                'total_value': total_value,
                'cash_balance': self.cash_balance,
                'positions_value': positions_value,
                'initial_capital': self.initial_capital,
                'total_return': total_return,
                'total_return_pct': total_return * 100,
                'realized_pnl': self.total_realized_pnl,
                'unrealized_pnl': self.total_unrealized_pnl,
                'daily_pnl': self.daily_pnl,
                'max_drawdown': self.max_drawdown,
                'max_drawdown_pct': self.max_drawdown * 100,
                'num_positions': len(self.positions),
                'leverage': positions_value / total_value if total_value > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"Error getting portfolio summary: {e}")
            return {}
    
    def calculate_position_size(
        self,
        symbol: str,
        signal_strength: float,
        current_price: float,
        volatility: float = 0.02,
        max_risk_per_trade: float = None
    ) -> float:
        """
        Calculate optimal position size based on risk management rules.
        
        Args:
            symbol: Trading symbol
            signal_strength: Signal confidence (0-1)
            current_price: Current market price
            volatility: Estimated volatility
            max_risk_per_trade: Maximum risk per trade as fraction of portfolio
            
        Returns:
            Position size in base currency units
        """
        try:
            max_risk = max_risk_per_trade or settings.risk_percentage
            total_value = self.get_portfolio_value()
            
            # Base risk amount
            risk_amount = total_value * max_risk
            
            # Adjust for signal strength
            adjusted_risk = risk_amount * signal_strength
            
            # Adjust for volatility (higher volatility = smaller position)
            volatility_adjustment = 1.0 / max(volatility, 0.01)
            position_value = adjusted_risk * volatility_adjustment
            
            # Convert to quantity
            position_size = position_value / current_price
            
            # Apply position limits
            max_position = self._get_max_position_size(symbol, total_value)
            position_size = min(position_size, max_position)
            
            return max(position_size, 0.0)
            
        except Exception as e:
            logger.error(f"Error calculating position size: {e}")
            return 0.0
    
    def _get_max_position_size(self, symbol: str, total_value: float) -> float:
        """Get maximum allowed position size for symbol."""
        try:
            # Default maximum position as percentage of portfolio
            default_max_pct = settings.max_position_size
            
            # Check symbol-specific limits
            if symbol in self.position_limits:
                max_pct = self.position_limits[symbol]
            else:
                max_pct = default_max_pct
            
            # Get current price (would need to be passed or fetched)
            # For now, use a reasonable estimate
            estimated_price = 50000 if symbol == 'BTC' else 3000 if symbol == 'ETH' else 1000
            
            max_value = total_value * max_pct
            max_quantity = max_value / estimated_price
            
            return max_quantity
            
        except Exception as e:
            logger.error(f"Error getting max position size: {e}")
            return 0.0
    
    def set_position_limit(self, symbol: str, max_percentage: float) -> None:
        """Set maximum position size limit for a symbol."""
        self.position_limits[symbol] = max_percentage
        logger.info(f"Position limit set for {symbol}: {max_percentage:.1%}")
    
    def check_risk_limits(self) -> Dict[str, Any]:
        """Check current portfolio against risk limits."""
        try:
            total_value = self.get_portfolio_value()
            violations = []
            warnings = []
            
            # Check individual position limits
            for symbol, position in self.positions.items():
                position_pct = abs(position.market_value) / total_value if total_value > 0 else 0
                max_pct = self.position_limits.get(symbol, settings.max_position_size)
                
                if position_pct > max_pct:
                    violations.append(f"{symbol} position ({position_pct:.1%}) exceeds limit ({max_pct:.1%})")
                elif position_pct > max_pct * 0.8:
                    warnings.append(f"{symbol} position ({position_pct:.1%}) near limit ({max_pct:.1%})")
            
            # Check total leverage
            positions_value = sum(abs(pos.market_value) for pos in self.positions.values())
            leverage = positions_value / total_value if total_value > 0 else 0
            max_leverage = settings.max_leverage
            
            if leverage > max_leverage:
                violations.append(f"Portfolio leverage ({leverage:.1f}x) exceeds limit ({max_leverage:.1f}x)")
            elif leverage > max_leverage * 0.8:
                warnings.append(f"Portfolio leverage ({leverage:.1f}x) near limit ({max_leverage:.1f}x)")
            
            # Check drawdown
            if self.max_drawdown > settings.max_drawdown:
                violations.append(f"Max drawdown ({self.max_drawdown:.1%}) exceeds limit ({settings.max_drawdown:.1%})")
            
            return {
                'violations': violations,
                'warnings': warnings,
                'risk_score': len(violations) * 2 + len(warnings),
                'total_value': total_value,
                'leverage': leverage,
                'max_drawdown': self.max_drawdown
            }
            
        except Exception as e:
            logger.error(f"Error checking risk limits: {e}")
            return {'violations': [], 'warnings': [], 'risk_score': 0}
    
    def create_snapshot(self) -> PortfolioSnapshot:
        """Create a portfolio snapshot for historical tracking."""
        try:
            summary = self.get_portfolio_summary()
            
            snapshot = PortfolioSnapshot(
                timestamp=datetime.now(),
                total_value=summary['total_value'],
                cash_balance=summary['cash_balance'],
                positions_value=summary['positions_value'],
                unrealized_pnl=summary['unrealized_pnl'],
                realized_pnl=summary['realized_pnl'],
                daily_pnl=summary['daily_pnl'],
                positions=self.positions.copy()
            )
            
            self.portfolio_history.append(snapshot)
            
            # Keep only last 1000 snapshots
            if len(self.portfolio_history) > 1000:
                self.portfolio_history = self.portfolio_history[-1000:]
            
            return snapshot
            
        except Exception as e:
            logger.error(f"Error creating portfolio snapshot: {e}")
            return None
    
    def get_performance_metrics(self, days: int = 30) -> Dict[str, Any]:
        """Calculate performance metrics over specified period."""
        try:
            if len(self.portfolio_history) < 2:
                return {}
            
            # Get snapshots from the last N days
            cutoff_time = datetime.now() - timedelta(days=days)
            recent_snapshots = [s for s in self.portfolio_history if s.timestamp >= cutoff_time]
            
            if len(recent_snapshots) < 2:
                return {}
            
            # Calculate returns
            start_value = recent_snapshots[0].total_value
            end_value = recent_snapshots[-1].total_value
            total_return = (end_value - start_value) / start_value
            
            # Calculate daily returns
            daily_returns = []
            for i in range(1, len(recent_snapshots)):
                prev_value = recent_snapshots[i-1].total_value
                curr_value = recent_snapshots[i].total_value
                daily_return = (curr_value - prev_value) / prev_value
                daily_returns.append(daily_return)
            
            if daily_returns:
                volatility = np.std(daily_returns) * np.sqrt(252)  # Annualized
                sharpe_ratio = (np.mean(daily_returns) * 252) / volatility if volatility > 0 else 0
            else:
                volatility = 0
                sharpe_ratio = 0
            
            return {
                'period_days': days,
                'total_return': total_return,
                'total_return_pct': total_return * 100,
                'annualized_return': (1 + total_return) ** (365 / days) - 1,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': self.max_drawdown,
                'num_trades': len([s for s in recent_snapshots if s.realized_pnl != 0]),
                'win_rate': self._calculate_win_rate(recent_snapshots)
            }
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    def _calculate_win_rate(self, snapshots: List[PortfolioSnapshot]) -> float:
        """Calculate win rate from snapshots."""
        try:
            winning_days = 0
            total_days = 0
            
            for i in range(1, len(snapshots)):
                prev_value = snapshots[i-1].total_value
                curr_value = snapshots[i].total_value
                
                if curr_value > prev_value:
                    winning_days += 1
                total_days += 1
            
            return winning_days / total_days if total_days > 0 else 0
            
        except Exception as e:
            logger.error(f"Error calculating win rate: {e}")
            return 0.0
    
    def reset_daily_pnl(self) -> None:
        """Reset daily P&L counter (call at start of each trading day)."""
        self.daily_pnl = 0.0
        logger.info("Daily P&L reset")
    
    def export_positions(self) -> pd.DataFrame:
        """Export current positions to DataFrame."""
        try:
            if not self.positions:
                return pd.DataFrame()
            
            data = []
            for symbol, position in self.positions.items():
                data.append({
                    'symbol': symbol,
                    'quantity': position.quantity,
                    'average_price': position.average_price,
                    'market_value': position.market_value,
                    'unrealized_pnl': position.unrealized_pnl,
                    'realized_pnl': position.realized_pnl,
                    'last_update': position.last_update
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            logger.error(f"Error exporting positions: {e}")
            return pd.DataFrame()
