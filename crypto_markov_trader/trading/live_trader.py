"""
Live trading orchestrator that coordinates all trading components.
"""

import asyncio
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json
from loguru import logger

from .execution_engine import ExecutionEngine, OrderType
from .portfolio_manager import PortfolioManager
from .risk_manager import RiskManager, RiskLevel
from ..models import MarkovRegimeModel, RegimeClassifier, TradingSignal
from ..llm_integration import LLMTradingAdvisor
from ..feature_engineering import FeaturePipeline
from ..data_collection import DataAggregator
from ..config import settings


class LiveTrader:
    """Live trading orchestrator."""
    
    def __init__(
        self,
        symbols: List[str] = None,
        initial_capital: float = None,
        model_path: Optional[str] = None
    ):
        """
        Initialize live trader.
        
        Args:
            symbols: List of symbols to trade
            initial_capital: Starting capital
            model_path: Path to trained model
        """
        self.symbols = symbols or ['BTC', 'ETH']
        self.initial_capital = initial_capital or settings.default_position_size * 100
        
        # Initialize components
        self.execution_engine = ExecutionEngine()
        self.portfolio_manager = PortfolioManager(self.initial_capital)
        self.risk_manager = RiskManager(self.portfolio_manager)
        self.feature_pipeline = FeaturePipeline()
        self.data_aggregator = DataAggregator()
        
        # Trading models
        self.models = {}  # symbol -> MarkovRegimeModel
        self.classifiers = {}  # symbol -> RegimeClassifier
        self.llm_advisor = LLMTradingAdvisor(use_local_model=False)
        
        # Trading state
        self.is_running = False
        self.trading_enabled = True
        self.last_signal_time = {}  # symbol -> datetime
        self.signal_cooldown = timedelta(minutes=5)  # Minimum time between signals
        
        # Performance tracking
        self.trading_stats = {
            'start_time': None,
            'total_signals': 0,
            'executed_trades': 0,
            'rejected_trades': 0,
            'total_pnl': 0.0
        }
        
        # Load models if provided
        if model_path:
            self._load_models(model_path)
        
        logger.info("🤖 Live Trader Initialization Complete")
        logger.info("=" * 50)
        logger.info(f"📊 Trading Symbols: {self.symbols}")
        logger.info(f"💰 Initial Capital: ${self.initial_capital:,.2f}")
        logger.info(f"⚙️  Components Loaded:")
        logger.info(f"   ✅ Execution Engine: Ready")
        logger.info(f"   ✅ Portfolio Manager: Ready")
        logger.info(f"   ✅ Risk Manager: Ready")
        logger.info(f"   ✅ Feature Pipeline: Ready")
        logger.info(f"   ✅ Data Aggregator: Ready")
        logger.info(f"   ✅ LLM Advisor: {'Available' if self.llm_advisor.is_available() else 'Rule-based fallback'}")
        logger.info("=" * 50)
    
    def _load_models(self, model_path: str) -> None:
        """Load trained models for trading."""
        try:
            import os
            import glob

            logger.info(f"🔍 Loading models from: {model_path}")

            for symbol in self.symbols:
                # Look for trained model files
                model_pattern = f"{model_path}/*{symbol}*model*.joblib"
                model_files = glob.glob(model_pattern)

                if model_files:
                    # Load the most recent model file
                    latest_model = max(model_files, key=os.path.getctime)
                    logger.info(f"📂 Loading model for {symbol}: {latest_model}")

                    model = MarkovRegimeModel()
                    model.load_model(latest_model)

                    self.models[symbol] = model
                    self.classifiers[symbol] = RegimeClassifier(model)

                    logger.info(f"✅ Model loaded for {symbol} - Components: {model.n_components}")

                else:
                    # Train a new model if none exists
                    logger.warning(f"⚠️  No trained model found for {symbol}, creating fallback model...")
                    # Create a fallback model for now - training will happen async later
                    model = MarkovRegimeModel()
                    model.is_fitted = False
                    self.models[symbol] = model
                    self.classifiers[symbol] = RegimeClassifier(model)

        except Exception as e:
            logger.error(f"❌ Error loading models: {e}")
            # Create fallback models
            for symbol in self.symbols:
                logger.warning(f"🔄 Creating fallback model for {symbol}")
                model = MarkovRegimeModel()
                model.is_fitted = False
                self.models[symbol] = model
                self.classifiers[symbol] = RegimeClassifier(model)

    async def _train_model_for_symbol(self, symbol: str) -> None:
        """Train a new model for a symbol if none exists."""
        try:
            from ..models import ModelTrainer

            logger.info(f"🎯 Training new model for {symbol}...")

            # Collect recent data for training
            end_time = datetime.now()
            start_time = end_time - timedelta(days=7)  # Use 7 days of data

            market_data = self.data_aggregator.hyperliquid_client.get_historical_data(
                symbol=symbol,
                interval="1h",
                start_time=start_time,
                end_time=end_time,
                limit=168  # 7 days * 24 hours
            )

            if not market_data.empty:
                # Create features
                features_df = self.feature_pipeline.create_features(market_data, symbol)

                if not features_df.empty:
                    # Train model
                    trainer = ModelTrainer()
                    model = trainer.train_hmm_model(features_df, symbol, n_components=3)

                    if model and model.is_fitted:
                        self.models[symbol] = model
                        self.classifiers[symbol] = RegimeClassifier(model)
                        logger.info(f"✅ Successfully trained model for {symbol}")
                    else:
                        logger.error(f"❌ Failed to train model for {symbol}")
                else:
                    logger.error(f"❌ No features generated for {symbol}")
            else:
                logger.error(f"❌ No market data available for {symbol}")

        except Exception as e:
            logger.error(f"❌ Error training model for {symbol}: {e}")

    async def start_trading(self) -> None:
        """Start the live trading loop."""
        try:
            if self.is_running:
                logger.warning("Trading is already running")
                return
            
            self.is_running = True
            self.trading_stats['start_time'] = datetime.now()
            
            logger.info("🚀 STARTING LIVE TRADING SYSTEM")
            logger.info("=" * 60)
            logger.info(f"🎯 Target Symbols: {', '.join(self.symbols)}")
            logger.info(f"💰 Portfolio Value: ${self.portfolio_manager.get_portfolio_value():,.2f}")
            logger.info(f"⚠️  Risk Score: {self.risk_manager.get_risk_score():.1f}/100")
            logger.info(f"🔄 Trading Interval: {settings.trading_interval}s")
            logger.info(f"📊 Signal Threshold: {settings.min_signal_strength:.1%}")
            logger.info("=" * 60)

            # Start main trading loop
            await self._trading_loop()
            
        except Exception as e:
            logger.error(f"Error starting trading: {e}")
            self.is_running = False
    
    async def stop_trading(self) -> None:
        """Stop live trading."""
        try:
            self.is_running = False
            logger.info("🛑 Stopping live trading...")
            
            # Cancel all active orders
            for symbol in self.symbols:
                active_orders = self.execution_engine.get_active_orders(symbol)
                for order in active_orders:
                    await self.execution_engine.cancel_order(order.id)
            
            # Generate final report
            await self._generate_trading_report()
            
            logger.info("Live trading stopped")
            
        except Exception as e:
            logger.error(f"Error stopping trading: {e}")
    
    async def _trading_loop(self) -> None:
        """Main trading loop."""
        try:
            while self.is_running:
                try:
                    # Update market data
                    await self._update_market_data()
                    
                    # Monitor risk
                    await self._monitor_risk()
                    
                    # Generate and process signals
                    if self.trading_enabled and not self.risk_manager.emergency_stop:
                        await self._process_trading_signals()
                    
                    # Update portfolio
                    await self._update_portfolio()
                    
                    # Cleanup old orders
                    await self.execution_engine.cleanup_completed_orders()
                    
                    # Wait before next iteration
                    await asyncio.sleep(settings.trading_interval)
                    
                except Exception as e:
                    logger.error(f"Error in trading loop: {e}")
                    await asyncio.sleep(10)  # Wait longer on error
                    
        except Exception as e:
            logger.error(f"Fatal error in trading loop: {e}")
            self.is_running = False
    
    async def _update_market_data(self) -> None:
        """Update market data for all symbols."""
        try:
            current_prices = {}
            
            for symbol in self.symbols:
                # Fetch real market data from Hyperliquid
                try:
                    end_time = datetime.now()
                    start_time = end_time - timedelta(minutes=5)

                    market_data = self.data_aggregator.hyperliquid_client.get_historical_data(
                        symbol=symbol,
                        interval="1m",
                        start_time=start_time,
                        end_time=end_time,
                        limit=5
                    )

                    if not market_data.empty:
                        current_price = float(market_data['close'].iloc[-1])
                        current_prices[symbol] = current_price
                        logger.debug(f"📊 {symbol}: ${current_price:,.2f}")
                    else:
                        logger.warning(f"⚠️  No market data for {symbol}")

                except Exception as e:
                    logger.error(f"❌ Error fetching data for {symbol}: {e}")
                    # Fallback to last known price if available
                    if hasattr(self, 'last_prices') and symbol in self.last_prices:
                        current_prices[symbol] = self.last_prices[symbol]

            # Store last known prices
            if not hasattr(self, 'last_prices'):
                self.last_prices = {}
            self.last_prices.update(current_prices)

            # Update portfolio with current prices
            if current_prices:
                self.portfolio_manager.update_market_prices(current_prices)
                logger.debug(f"📈 Market data updated: {len(current_prices)} symbols")
            
        except Exception as e:
            logger.error(f"Error updating market data: {e}")
    
    async def _monitor_risk(self) -> None:
        """Monitor portfolio risk and handle alerts."""
        try:
            alerts = self.risk_manager.monitor_portfolio_risk()
            
            for alert in alerts:
                logger.warning(f"Risk Alert: {alert.message}")
                
                if alert.level == RiskLevel.CRITICAL and alert.action_required:
                    # Take immediate action for critical alerts
                    await self._handle_critical_risk_alert(alert)
                
        except Exception as e:
            logger.error(f"Error monitoring risk: {e}")
    
    async def _handle_critical_risk_alert(self, alert) -> None:
        """Handle critical risk alerts."""
        try:
            if "Daily loss" in alert.message or "Drawdown" in alert.message:
                # Emergency stop trading
                self.trading_enabled = False
                logger.critical(f"Trading disabled due to: {alert.message}")
                
                # Cancel all active orders
                await self.execution_engine.emergency_cancel_all()
                
                # Optionally close all positions (implement based on strategy)
                # await self._close_all_positions()
                
        except Exception as e:
            logger.error(f"Error handling critical risk alert: {e}")
    
    async def _process_trading_signals(self) -> None:
        """Process trading signals for all symbols."""
        try:
            for symbol in self.symbols:
                await self._process_symbol_signals(symbol)
                
        except Exception as e:
            logger.error(f"Error processing trading signals: {e}")
    
    async def _process_symbol_signals(self, symbol: str) -> None:
        """Process trading signals for a specific symbol."""
        try:
            # Check signal cooldown
            if symbol in self.last_signal_time:
                time_since_last = datetime.now() - self.last_signal_time[symbol]
                if time_since_last < self.signal_cooldown:
                    return
            
            # Get recent features (in production, this would be real-time data)
            features_df = await self._get_recent_features(symbol)
            if features_df.empty:
                return
            
            # Generate regime-based signal
            if symbol in self.classifiers:
                signals_df = self.classifiers[symbol].generate_signals(features_df.tail(1))
                
                if not signals_df.empty:
                    latest_signal = signals_df.iloc[-1]
                    signal = TradingSignal(latest_signal['final_signal'])
                    signal_strength = latest_signal['signal_strength']
                    regime = latest_signal['regime_label']
                    
                    # Get LLM recommendation
                    llm_recommendation = await self._get_llm_recommendation(
                        symbol, features_df, signal, regime, signal_strength
                    )
                    
                    # Execute trade if conditions are met
                    await self._execute_signal(
                        symbol, signal, signal_strength, regime, llm_recommendation
                    )
                    
                    self.last_signal_time[symbol] = datetime.now()
                    self.trading_stats['total_signals'] += 1
            
        except Exception as e:
            logger.error(f"Error processing signals for {symbol}: {e}")
    
    async def _get_recent_features(self, symbol: str) -> pd.DataFrame:
        """Get recent features for a symbol."""
        try:
            # Fetch real market data and create features
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=2)  # Get 2 hours of data for features

            # Get market data
            market_data = self.data_aggregator.hyperliquid_client.get_historical_data(
                symbol=symbol,
                interval="1m",
                start_time=start_time,
                end_time=end_time,
                limit=120
            )

            if market_data.empty:
                logger.warning(f"⚠️  No market data available for {symbol}")
                return pd.DataFrame()

            # Create features using the feature pipeline
            features_df = self.feature_pipeline.create_features(market_data, symbol)

            if not features_df.empty:
                logger.debug(f"📊 Generated {len(features_df.columns)} features for {symbol}")
                return features_df
            else:
                logger.warning(f"⚠️  No features generated for {symbol}")
                return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"Error getting recent features for {symbol}: {e}")
            return pd.DataFrame()
    
    async def _get_llm_recommendation(
        self,
        symbol: str,
        features_df: pd.DataFrame,
        signal: TradingSignal,
        regime: str,
        signal_strength: float
    ) -> Dict[str, Any]:
        """Get LLM-powered trading recommendation."""
        try:
            current_price = features_df['close'].iloc[-1]
            
            recommendation = self.llm_advisor.generate_quick_recommendation(
                symbol=symbol,
                current_price=current_price,
                signal=signal,
                regime=regime,
                confidence=signal_strength
            )
            
            return recommendation
            
        except Exception as e:
            logger.error(f"Error getting LLM recommendation: {e}")
            return {}
    
    async def _execute_signal(
        self,
        symbol: str,
        signal: TradingSignal,
        signal_strength: float,
        regime: str,
        llm_recommendation: Dict[str, Any]
    ) -> None:
        """Execute trading signal."""
        try:
            if signal == TradingSignal.HOLD:
                return
            
            # Get current price
            current_position = self.portfolio_manager.get_position(symbol)
            current_price = 50000 if symbol == 'BTC' else 3000  # Mock price
            
            # Calculate position size
            position_size = self.portfolio_manager.calculate_position_size(
                symbol=symbol,
                signal_strength=signal_strength,
                current_price=current_price,
                volatility=0.02
            )
            
            if position_size == 0:
                return
            
            # Determine trade side
            if signal in [TradingSignal.BUY, TradingSignal.STRONG_BUY]:
                side = 'buy'
                # Close short position first if exists
                if current_position and current_position.is_short:
                    await self._close_position(symbol, current_position)
            else:  # SELL or STRONG_SELL
                side = 'sell'
                # Close long position first if exists
                if current_position and current_position.is_long:
                    await self._close_position(symbol, current_position)
            
            # Risk check
            approved, warnings = self.risk_manager.check_pre_trade_risk(
                symbol=symbol,
                side=side,
                quantity=position_size,
                price=current_price,
                signal_strength=signal_strength
            )
            
            if not approved:
                logger.warning(f"Trade rejected for {symbol}: {warnings}")
                self.trading_stats['rejected_trades'] += 1
                return
            
            # Execute trade
            order = await self.execution_engine.submit_order(
                symbol=symbol,
                side=side,
                quantity=position_size,
                order_type=OrderType.MARKET
            )
            
            if order.status.value in ['filled', 'partially_filled']:
                # Update portfolio
                quantity_change = position_size if side == 'buy' else -position_size
                self.portfolio_manager.update_position(
                    symbol=symbol,
                    quantity_change=quantity_change,
                    price=order.average_fill_price,
                    fees=order.fees
                )
                
                self.trading_stats['executed_trades'] += 1
                
                logger.info(f"Trade executed: {side.upper()} {position_size:.6f} {symbol} @ ${order.average_fill_price:.2f}")
                logger.info(f"Signal: {signal.name}, Strength: {signal_strength:.1%}, Regime: {regime}")
                
                if llm_recommendation.get('analysis'):
                    logger.info(f"LLM Analysis: {llm_recommendation['analysis']}")
            else:
                logger.warning(f"Trade failed for {symbol}: {order.status}")
                self.trading_stats['rejected_trades'] += 1
            
        except Exception as e:
            logger.error(f"Error executing signal for {symbol}: {e}")
    
    async def _close_position(self, symbol: str, position) -> None:
        """Close an existing position."""
        try:
            side = 'sell' if position.is_long else 'buy'
            quantity = abs(position.quantity)
            current_price = 50000 if symbol == 'BTC' else 3000  # Mock price
            
            order = await self.execution_engine.submit_order(
                symbol=symbol,
                side=side,
                quantity=quantity,
                order_type=OrderType.MARKET
            )
            
            if order.status.value in ['filled', 'partially_filled']:
                quantity_change = -quantity if position.is_long else quantity
                self.portfolio_manager.update_position(
                    symbol=symbol,
                    quantity_change=quantity_change,
                    price=order.average_fill_price,
                    fees=order.fees
                )
                
                logger.info(f"Position closed: {symbol}")
            
        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
    
    async def _update_portfolio(self) -> None:
        """Update portfolio and create snapshot."""
        try:
            # Create portfolio snapshot
            snapshot = self.portfolio_manager.create_snapshot()
            
            if snapshot:
                # Update trading stats
                self.trading_stats['total_pnl'] = snapshot.realized_pnl + snapshot.unrealized_pnl
                
                # Log portfolio status periodically
                if datetime.now().minute % 15 == 0:  # Every 15 minutes
                    summary = self.portfolio_manager.get_portfolio_summary()
                    logger.info(f"Portfolio Value: ${summary['total_value']:,.2f}, "
                              f"P&L: ${summary['realized_pnl'] + summary['unrealized_pnl']:,.2f}, "
                              f"Positions: {summary['num_positions']}")
            
        except Exception as e:
            logger.error(f"Error updating portfolio: {e}")
    
    async def _generate_trading_report(self) -> Dict[str, Any]:
        """Generate comprehensive trading report."""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            risk_report = self.risk_manager.get_risk_report()
            execution_stats = self.execution_engine.get_execution_stats()
            
            report = {
                'trading_session': {
                    'start_time': self.trading_stats['start_time'],
                    'end_time': datetime.now(),
                    'duration_hours': (datetime.now() - self.trading_stats['start_time']).total_seconds() / 3600,
                    'symbols_traded': self.symbols
                },
                'performance': portfolio_summary,
                'trading_stats': self.trading_stats,
                'execution_stats': execution_stats,
                'risk_report': risk_report,
                'final_positions': self.portfolio_manager.export_positions().to_dict('records') if not self.portfolio_manager.export_positions().empty else []
            }
            
            logger.info("Trading report generated")
            return report
            
        except Exception as e:
            logger.error(f"Error generating trading report: {e}")
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get current trading status."""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            
            return {
                'is_running': self.is_running,
                'trading_enabled': self.trading_enabled,
                'emergency_stop': self.risk_manager.emergency_stop,
                'symbols': self.symbols,
                'portfolio_value': portfolio_summary.get('total_value', 0),
                'daily_pnl': portfolio_summary.get('daily_pnl', 0),
                'num_positions': portfolio_summary.get('num_positions', 0),
                'risk_score': self.risk_manager.get_risk_score(),
                'trading_stats': self.trading_stats,
                'last_update': datetime.now()
            }
            
        except Exception as e:
            logger.error(f"Error getting status: {e}")
            return {'error': str(e)}
    
    def enable_trading(self) -> None:
        """Enable trading."""
        self.trading_enabled = True
        logger.info("Trading enabled")
    
    def disable_trading(self) -> None:
        """Disable trading."""
        self.trading_enabled = False
        logger.info("Trading disabled")
    
    async def emergency_stop(self) -> None:
        """Emergency stop all trading activity."""
        try:
            self.trading_enabled = False
            self.risk_manager.emergency_stop = True
            
            # Cancel all orders
            await self.execution_engine.emergency_cancel_all()
            
            logger.critical("EMERGENCY STOP ACTIVATED")
            
        except Exception as e:
            logger.error(f"Error in emergency stop: {e}")
