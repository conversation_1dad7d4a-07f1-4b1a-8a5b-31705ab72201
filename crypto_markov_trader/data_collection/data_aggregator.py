"""
Data aggregator for synchronizing market data and sentiment data.
"""

import pandas as pd
from typing import Dict, List, Optional
from datetime import datetime, timedelta
from loguru import logger

from ..utils import save_dataframe_to_parquet, load_dataframe_from_parquet
from .hyperliquid_client import HyperliquidClient
from .twitter_client import TwitterClient


class DataAggregator:
    """Aggregates and synchronizes market data with sentiment data."""

    def __init__(self, data_dir: str = "data/processed"):
        self.data_dir = data_dir

        # Initialize clients
        self.hyperliquid_client = HyperliquidClient()
        self.twitter_client = TwitterClient()

        # Initialize sentiment analyzer lazily to avoid circular import
        self._sentiment_analyzer = None

        logger.info("DataAggregator initialized with market and twitter clients")

    @property
    def sentiment_analyzer(self):
        """Lazy initialization of sentiment analyzer to avoid circular imports."""
        if self._sentiment_analyzer is None:
            from ..feature_engineering import SentimentAnalyzer
            self._sentiment_analyzer = SentimentAnalyzer()
        return self._sentiment_analyzer
        
    def aggregate_sentiment_by_timeframe(
        self, 
        tweets: List[Dict], 
        timeframe: str = "1min"
    ) -> pd.DataFrame:
        """
        Aggregate tweet sentiment scores by timeframe.
        
        Args:
            tweets: List of tweet dictionaries with sentiment scores
            timeframe: Aggregation timeframe ('1min', '5min', '1h')
            
        Returns:
            DataFrame with aggregated sentiment scores
        """
        if not tweets:
            return pd.DataFrame()
        
        try:
            # Convert to DataFrame
            df = pd.DataFrame(tweets)
            
            # Ensure we have required columns
            required_cols = ['created_at', 'sentiment_score']
            if not all(col in df.columns for col in required_cols):
                logger.error(f"Missing required columns: {required_cols}")
                return pd.DataFrame()
            
            # Convert timestamp
            df['timestamp'] = pd.to_datetime(df['created_at'])
            df.set_index('timestamp', inplace=True)
            
            # Resample and aggregate
            agg_functions = {
                'sentiment_score': ['mean', 'std', 'count'],
                'followers_count': 'sum',
                'like_count': 'sum',
                'retweet_count': 'sum'
            }
            
            # Handle missing columns gracefully
            available_agg = {}
            for col, func in agg_functions.items():
                if col in df.columns:
                    available_agg[col] = func
            
            if not available_agg:
                logger.warning("No aggregatable columns found")
                return pd.DataFrame()
            
            # Resample data
            resampled = df.resample(timeframe).agg(available_agg)
            
            # Flatten column names
            resampled.columns = ['_'.join(col).strip() if isinstance(col, tuple) else col 
                               for col in resampled.columns]
            
            # Fill missing values
            resampled = resampled.fillna(0)
            
            logger.info(f"Aggregated {len(tweets)} tweets into {len(resampled)} {timeframe} intervals")
            return resampled
            
        except Exception as e:
            logger.error(f"Error aggregating sentiment data: {e}")
            return pd.DataFrame()
    
    def synchronize_data(
        self, 
        market_data: pd.DataFrame, 
        sentiment_data: pd.DataFrame,
        method: str = "forward_fill"
    ) -> pd.DataFrame:
        """
        Synchronize market data with sentiment data.
        
        Args:
            market_data: OHLCV market data with timestamp index
            sentiment_data: Aggregated sentiment data with timestamp index
            method: Synchronization method ('forward_fill', 'interpolate', 'drop')
            
        Returns:
            Synchronized DataFrame with both market and sentiment data
        """
        try:
            if market_data.empty or sentiment_data.empty:
                logger.warning("Empty data provided for synchronization")
                return market_data.copy() if not market_data.empty else pd.DataFrame()
            
            # Ensure both DataFrames have datetime index
            if not isinstance(market_data.index, pd.DatetimeIndex):
                logger.error("Market data must have datetime index")
                return market_data.copy()
            
            if not isinstance(sentiment_data.index, pd.DatetimeIndex):
                logger.error("Sentiment data must have datetime index")
                return market_data.copy()
            
            # Merge data on timestamp
            combined = market_data.join(sentiment_data, how='left', rsuffix='_sentiment')
            
            # Handle missing sentiment data based on method
            if method == "forward_fill":
                # Forward fill sentiment values
                sentiment_cols = [col for col in combined.columns if 'sentiment' in col.lower()]
                combined[sentiment_cols] = combined[sentiment_cols].fillna(method='ffill')
                
            elif method == "interpolate":
                # Interpolate missing values
                sentiment_cols = [col for col in combined.columns if 'sentiment' in col.lower()]
                combined[sentiment_cols] = combined[sentiment_cols].interpolate()
                
            elif method == "drop":
                # Drop rows with missing sentiment data
                combined = combined.dropna()
            
            # Fill any remaining NaN values with 0
            combined = combined.fillna(0)
            
            logger.info(f"Synchronized data: {len(combined)} rows with {len(combined.columns)} columns")
            return combined
            
        except Exception as e:
            logger.error(f"Error synchronizing data: {e}")
            return market_data.copy()
    
    def save_synchronized_data(
        self, 
        data: pd.DataFrame, 
        symbol: str, 
        timeframe: str = "1min"
    ) -> None:
        """
        Save synchronized data to file.
        
        Args:
            data: Synchronized DataFrame
            symbol: Trading symbol
            timeframe: Data timeframe
        """
        try:
            filename = f"{symbol}_{timeframe}_synchronized.parquet"
            filepath = f"{self.data_dir}/{filename}"
            save_dataframe_to_parquet(data, filepath)
            
        except Exception as e:
            logger.error(f"Error saving synchronized data: {e}")
    
    def load_synchronized_data(
        self, 
        symbol: str, 
        timeframe: str = "1min"
    ) -> Optional[pd.DataFrame]:
        """
        Load synchronized data from file.
        
        Args:
            symbol: Trading symbol
            timeframe: Data timeframe
            
        Returns:
            Synchronized DataFrame or None if not found
        """
        try:
            filename = f"{symbol}_{timeframe}_synchronized.parquet"
            filepath = f"{self.data_dir}/{filename}"
            return load_dataframe_from_parquet(filepath)
            
        except Exception as e:
            logger.error(f"Error loading synchronized data: {e}")
            return None
