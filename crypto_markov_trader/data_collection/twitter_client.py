"""
Twitter/X API client for collecting sentiment data.
"""

import tweepy
import json
import os
from typing import List, Dict, Optional, Generator
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger

from ..config import settings


class TwitterClient:
    """Client for collecting tweets and sentiment data from Twitter/X."""
    
    def __init__(self):
        self.api = None
        self.client = None

        # Rate limit management
        self.cache_dir = "data/twitter_cache"
        self.rate_limit_window = 900  # 15 minutes in seconds
        self.max_requests_per_window = 300  # Twitter API v2 limit

        # Track rate limit status
        self.last_rate_limit_time = None
        self.rate_limit_reset_time = None

        # Create cache directory
        os.makedirs(self.cache_dir, exist_ok=True)

        self._setup_api()
        
    def _setup_api(self) -> None:
        """Initialize Twitter API clients."""
        try:
            if not settings.twitter_bearer_token:
                logger.warning("Twitter Bearer Token not configured")
                return
            
            # Initialize Twitter API v2 client
            self.client = tweepy.Client(
                bearer_token=settings.twitter_bearer_token,
                consumer_key=settings.twitter_api_key,
                consumer_secret=settings.twitter_api_secret,
                access_token=settings.twitter_access_token,
                access_token_secret=settings.twitter_access_token_secret,
                wait_on_rate_limit=True
            )
            
            # Initialize API v1.1 for additional features if needed
            if all([settings.twitter_api_key, settings.twitter_api_secret, 
                   settings.twitter_access_token, settings.twitter_access_token_secret]):
                auth = tweepy.OAuth1UserHandler(
                    settings.twitter_api_key,
                    settings.twitter_api_secret,
                    settings.twitter_access_token,
                    settings.twitter_access_token_secret
                )
                self.api = tweepy.API(auth, wait_on_rate_limit=True)
            
            logger.info("Twitter API clients initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing Twitter API: {e}")

    def _get_cache_filename(self, query: str, max_results: int) -> str:
        """Get cache filename for query."""
        # For BTC queries, use the mock cache file
        if "BTC" in query.upper():
            return os.path.join(self.cache_dir, "tweets_mock_btc.json")

        # For other queries, use hash-based filename
        import hashlib
        query_hash = hashlib.md5(f"{query}_{max_results}".encode()).hexdigest()
        return os.path.join(self.cache_dir, f"tweets_{query_hash}.json")

    def _is_cache_valid(self, cache_file: str) -> bool:
        """Check if cache file is still valid (within rate limit window)."""
        if not os.path.exists(cache_file):
            return False

        try:
            with open(cache_file, 'r') as f:
                data = json.load(f)

            cache_time = datetime.fromisoformat(data.get('timestamp', ''))
            time_diff = (datetime.now() - cache_time).total_seconds()

            # Cache is valid for 10 minutes
            return time_diff < 600

        except Exception as e:
            logger.error(f"Error checking cache validity: {e}")
            return False

    def _save_to_cache(self, cache_file: str, tweets: List[Dict], query: str) -> None:
        """Save tweets to cache file."""
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'query': query,
                'tweet_count': len(tweets),
                'tweets': tweets
            }

            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)

            logger.info(f"💾 Saved {len(tweets)} tweets to cache")

        except Exception as e:
            logger.error(f"Error saving to cache: {e}")

    def _load_from_cache(self, cache_file: str) -> List[Dict]:
        """Load tweets from cache file."""
        try:
            with open(cache_file, 'r') as f:
                data = json.load(f)

            tweets = data.get('tweets', [])
            cache_time = data.get('timestamp', '')

            logger.info(f"📂 Loaded {len(tweets)} tweets from cache (cached at {cache_time})")
            return tweets

        except Exception as e:
            logger.error(f"Error loading from cache: {e}")
            return []

    def search_tweets(
        self,
        query: str,
        max_results: int = 100,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        use_cache: bool = True
    ) -> List[Dict]:
        """
        Search for tweets matching the given query with rate limit management.

        Args:
            query: Search query (e.g., "Bitcoin OR $BTC OR crypto")
            max_results: Maximum number of tweets to return
            start_time: Start time for search
            end_time: End time for search
            use_cache: Whether to use cached data if available

        Returns:
            List of tweet dictionaries
        """
        if not self.client:
            logger.error("Twitter client not initialized")
            return []
        
        # Check cache first
        cache_file = self._get_cache_filename(query, max_results)
        if use_cache and self._is_cache_valid(cache_file):
            logger.info(f"🔄 Using cached tweets for query: '{query}' (cache valid)")
            return self._load_from_cache(cache_file)

        # Check if we're rate limited
        if self._is_rate_limited():
            logger.warning(f"⚠️  Rate limited! Using any cached data for query: '{query}'")
            if os.path.exists(cache_file):
                return self._load_from_cache(cache_file)
            else:
                logger.error(f"❌ No cached data available for rate-limited query: '{query}'")
                return []

        try:
            logger.info(f"🐦 Fetching fresh tweets: '{query}' (max: {max_results})")
            tweets = []

            # Use Twitter API v2 search
            response = self.client.search_recent_tweets(
                query=query,
                max_results=min(max_results, 100),  # API limit
                start_time=start_time,
                end_time=end_time,
                tweet_fields=['created_at', 'author_id', 'public_metrics', 'context_annotations', 'lang'],
                user_fields=['public_metrics', 'verified'],
                expansions=['author_id']
            )
            
            if not response.data:
                logger.info(f"No tweets found for query: {query}")
                return []
            
            # Extract user information
            users = {}
            if response.includes and 'users' in response.includes:
                users = {user.id: user for user in response.includes['users']}
            
            # Process tweets
            for tweet in response.data:
                user = users.get(tweet.author_id, {})
                
                tweet_data = {
                    'id': tweet.id,
                    'text': tweet.text,
                    'created_at': tweet.created_at,
                    'author_id': tweet.author_id,
                    'language': tweet.lang,
                    'retweet_count': tweet.public_metrics.get('retweet_count', 0),
                    'like_count': tweet.public_metrics.get('like_count', 0),
                    'reply_count': tweet.public_metrics.get('reply_count', 0),
                    'quote_count': tweet.public_metrics.get('quote_count', 0),
                    'followers_count': getattr(user, 'followers_count', 0),
                    'verified': getattr(user, 'verified', False)
                }
                
                tweets.append(tweet_data)
            
            logger.info(f"✅ Collected {len(tweets)} fresh tweets for query: {query}")

            # Save to cache
            if tweets:
                self._save_to_cache(cache_file, tweets, query)

            return tweets

        except Exception as e:
            error_message = str(e)
            logger.error(f"❌ Error searching tweets: {error_message}")

            # Check if this is a rate limit error
            if "rate limit" in error_message.lower() or "429" in error_message:
                self._handle_rate_limit_error(error_message)

            # Try to return cached data as fallback
            if os.path.exists(cache_file):
                logger.info("🔄 Falling back to cached data due to API error")
                return self._load_from_cache(cache_file)

            return []

    def _is_rate_limited(self) -> bool:
        """Check if we're currently rate limited."""
        if self.rate_limit_reset_time is None:
            return False

        # Check if rate limit has expired
        if datetime.now() >= self.rate_limit_reset_time:
            self.rate_limit_reset_time = None
            self.last_rate_limit_time = None
            return False

        return True

    def _handle_rate_limit_error(self, error_message: str):
        """Handle rate limit error and set reset time."""
        logger.warning(f"🚫 Twitter rate limit hit: {error_message}")
        self.last_rate_limit_time = datetime.now()
        # Set rate limit reset time to 15 minutes from now
        self.rate_limit_reset_time = datetime.now() + timedelta(minutes=15)
        logger.info(f"⏰ Rate limit will reset at: {self.rate_limit_reset_time}")

    def get_crypto_tweets(
        self, 
        symbols: List[str], 
        max_results: int = 100,
        hours_back: int = 1
    ) -> List[Dict]:
        """
        Get tweets related to cryptocurrency symbols.
        
        Args:
            symbols: List of crypto symbols (e.g., ['BTC', 'ETH'])
            max_results: Maximum number of tweets to return
            hours_back: How many hours back to search
            
        Returns:
            List of tweet dictionaries
        """
        # Build search query
        symbol_queries = []
        for symbol in symbols:
            symbol_queries.extend([
                symbol,
                f"${symbol}",
                f"#{symbol}",
                f"{symbol}USD",
                f"{symbol}/USD"
            ])
        
        # Add general crypto terms
        crypto_terms = ["bitcoin", "ethereum", "crypto", "cryptocurrency", "blockchain"]
        
        # Combine all terms with OR
        all_terms = symbol_queries + crypto_terms
        query = " OR ".join(all_terms)
        
        # Add filters to reduce noise
        query += " -is:retweet lang:en"  # Exclude retweets, English only
        
        # Set time range
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(hours=hours_back)
        
        return self.search_tweets(
            query=query,
            max_results=max_results,
            start_time=start_time,
            end_time=end_time
        )
    
    def stream_tweets(self, keywords: List[str]) -> Generator[Dict, None, None]:
        """
        Stream tweets in real-time for given keywords.
        
        Args:
            keywords: List of keywords to track
            
        Yields:
            Tweet dictionaries
        """
        if not self.client:
            logger.error("Twitter client not initialized")
            return
        
        try:
            # Note: This is a simplified implementation
            # For production, you'd want to use Twitter's streaming API
            logger.info(f"Starting tweet stream for keywords: {keywords}")
            
            # For now, we'll simulate streaming by polling
            while True:
                tweets = self.get_crypto_tweets(keywords, max_results=10, hours_back=1)
                for tweet in tweets:
                    yield tweet
                
                # Wait before next poll (in production, use actual streaming)
                import time
                time.sleep(60)  # Poll every minute
                
        except Exception as e:
            logger.error(f"Error in tweet streaming: {e}")
    
    def is_configured(self) -> bool:
        """Check if Twitter API is properly configured."""
        return self.client is not None
