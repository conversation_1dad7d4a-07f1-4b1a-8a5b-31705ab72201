"""
Dynamic prompt builder for LLM trading recommendations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from loguru import logger

from ..models import TradingSignal
from ..config import settings


class PromptBuilder:
    """Build dynamic prompts for LLM trading analysis."""
    
    def __init__(self):
        self.base_prompt_template = self._load_base_template()
        self.market_context_template = self._load_market_context_template()
        self.technical_analysis_template = self._load_technical_template()
        self.sentiment_analysis_template = self._load_sentiment_template()
        
    def _load_base_template(self) -> str:
        """Load the base prompt template."""
        return """You are an expert cryptocurrency trading analyst with deep knowledge of:
- Technical analysis and market indicators
- Market regime identification and transitions
- Risk management and position sizing
- Sentiment analysis and market psychology
- Fundamental analysis of crypto markets

Your task is to provide a comprehensive trading recommendation based on the provided data.
Be specific, actionable, and include your reasoning process.

IMPORTANT GUIDELINES:
- Consider both technical indicators and market sentiment
- Account for current market regime and recent transitions
- Provide specific entry/exit levels when possible
- Include risk management recommendations
- Explain your confidence level and key assumptions
- Consider broader market context and potential catalysts

"""

    def _load_market_context_template(self) -> str:
        """Load market context template."""
        return """
MARKET CONTEXT:
==============
Symbol: {symbol}
Current Price: ${current_price:,.2f}
Timestamp: {timestamp}
Market Regime: {current_regime}
Regime Confidence: {regime_confidence:.1%}

Recent Price Action:
- 1H Change: {price_change_1h:+.2%}
- 4H Change: {price_change_4h:+.2%}
- 24H Change: {price_change_24h:+.2%}
- 7D Change: {price_change_7d:+.2%}

Volume Analysis:
- Current Volume: {current_volume:,.0f}
- Volume vs Average: {volume_ratio:.1f}x
- Volume Trend: {volume_trend}

"""

    def _load_technical_template(self) -> str:
        """Load technical analysis template."""
        return """
TECHNICAL ANALYSIS:
==================
Trend Indicators:
- EMA(12): ${ema_12:,.2f} | Price vs EMA: {price_vs_ema12:+.2%}
- EMA(26): ${ema_26:,.2f} | Price vs EMA: {price_vs_ema26:+.2%}
- SMA(50): ${sma_50:,.2f} | Price vs SMA: {price_vs_sma50:+.2%}
- EMA Cross Signal: {ema_cross_signal}

Momentum Indicators:
- RSI(14): {rsi:.1f} | Status: {rsi_status}
- MACD: {macd:.2f} | Signal: {macd_signal:.2f} | Histogram: {macd_histogram:.2f}
- MACD Status: {macd_status}

Volatility Indicators:
- ATR: {atr:.2f} | ATR%: {atr_percent:.2%}
- Bollinger Bands: Lower=${bb_lower:,.2f}, Upper=${bb_upper:,.2f}
- BB Position: {bb_position:.1%} | BB Width: {bb_width:.2%}
- Volatility Regime: {volatility_regime}

Support/Resistance:
- Support Level: ${support_level:,.2f}
- Resistance Level: ${resistance_level:,.2f}
- Distance to Support: {distance_to_support:+.2%}
- Distance to Resistance: {distance_to_resistance:+.2%}

"""

    def _load_sentiment_template(self) -> str:
        """Load sentiment analysis template."""
        return """
SENTIMENT ANALYSIS:
==================
Overall Sentiment: {overall_sentiment}
Sentiment Score: {sentiment_score:+.3f} (Range: -1 to +1)
Sentiment Confidence: {sentiment_confidence:.1%}

Detailed Sentiment Breakdown:
- Positive: {sentiment_positive:.1%}
- Neutral: {sentiment_neutral:.1%}
- Negative: {sentiment_negative:.1%}

Recent Sentiment Trend:
- 1H Trend: {sentiment_trend_1h}
- 4H Trend: {sentiment_trend_4h}
- Sentiment Volatility: {sentiment_volatility:.3f}

Social Media Activity:
- Tweet Volume: {tweet_volume} (vs average: {tweet_volume_ratio:.1f}x)
- Engagement Rate: {engagement_rate:.1%}
- Top Keywords: {top_keywords}

"""

    def build_comprehensive_prompt(
        self,
        symbol: str,
        current_data: pd.Series,
        regime_info: Dict[str, Any],
        technical_indicators: Dict[str, float],
        sentiment_data: Optional[Dict[str, Any]] = None,
        recent_signals: Optional[List[Dict]] = None,
        market_news: Optional[List[str]] = None
    ) -> str:
        """
        Build a comprehensive prompt for LLM analysis.
        
        Args:
            symbol: Trading symbol
            current_data: Current market data
            regime_info: Current regime information
            technical_indicators: Technical analysis data
            sentiment_data: Sentiment analysis data
            recent_signals: Recent trading signals
            market_news: Recent market news headlines
            
        Returns:
            Complete prompt string
        """
        try:
            prompt_parts = [self.base_prompt_template]
            
            # Add market context
            market_context = self._build_market_context(symbol, current_data, regime_info)
            prompt_parts.append(market_context)
            
            # Add technical analysis
            technical_context = self._build_technical_context(technical_indicators, current_data)
            prompt_parts.append(technical_context)
            
            # Add sentiment analysis if available
            if sentiment_data:
                sentiment_context = self._build_sentiment_context(sentiment_data)
                prompt_parts.append(sentiment_context)
            
            # Add recent signals context
            if recent_signals:
                signals_context = self._build_signals_context(recent_signals)
                prompt_parts.append(signals_context)
            
            # Add market news context
            if market_news:
                news_context = self._build_news_context(market_news)
                prompt_parts.append(news_context)
            
            # Add the final request
            final_request = self._build_final_request()
            prompt_parts.append(final_request)
            
            return "\n".join(prompt_parts)
            
        except Exception as e:
            logger.error(f"Error building comprehensive prompt: {e}")
            return self._build_fallback_prompt(symbol, current_data)
    
    def _build_market_context(
        self, 
        symbol: str, 
        current_data: pd.Series, 
        regime_info: Dict[str, Any]
    ) -> str:
        """Build market context section."""
        try:
            # Calculate price changes (mock data for now)
            current_price = current_data.get('close', 0)
            
            context_data = {
                'symbol': symbol,
                'current_price': current_price,
                'timestamp': current_data.name if hasattr(current_data, 'name') else datetime.now(),
                'current_regime': regime_info.get('regime', 'Unknown'),
                'regime_confidence': regime_info.get('confidence', 0.5),
                'price_change_1h': np.random.normal(0, 0.01),  # Mock data
                'price_change_4h': np.random.normal(0, 0.02),
                'price_change_24h': np.random.normal(0, 0.05),
                'price_change_7d': np.random.normal(0, 0.10),
                'current_volume': current_data.get('volume', 0),
                'volume_ratio': current_data.get('volume_ratio', 1.0),
                'volume_trend': 'Increasing' if current_data.get('volume_ratio', 1.0) > 1.2 else 'Decreasing' if current_data.get('volume_ratio', 1.0) < 0.8 else 'Stable'
            }
            
            return self.market_context_template.format(**context_data)
            
        except Exception as e:
            logger.error(f"Error building market context: {e}")
            return "MARKET CONTEXT: Error loading market data\n"
    
    def _build_technical_context(
        self, 
        technical_indicators: Dict[str, float], 
        current_data: pd.Series
    ) -> str:
        """Build technical analysis section."""
        try:
            current_price = current_data.get('close', 0)
            
            # Calculate derived metrics
            ema_12 = technical_indicators.get('ema_12', current_price)
            ema_26 = technical_indicators.get('ema_26', current_price)
            sma_50 = technical_indicators.get('sma_50', current_price)
            
            rsi = technical_indicators.get('rsi', 50)
            rsi_status = 'Overbought' if rsi > 70 else 'Oversold' if rsi < 30 else 'Neutral'
            
            macd = technical_indicators.get('macd', 0)
            macd_signal = technical_indicators.get('macd_signal', 0)
            macd_histogram = technical_indicators.get('macd_histogram', 0)
            macd_status = 'Bullish' if macd > macd_signal else 'Bearish'
            
            bb_lower = technical_indicators.get('bb_lower', current_price * 0.98)
            bb_upper = technical_indicators.get('bb_upper', current_price * 1.02)
            bb_position = (current_price - bb_lower) / (bb_upper - bb_lower) if bb_upper > bb_lower else 0.5
            bb_width = (bb_upper - bb_lower) / current_price if current_price > 0 else 0
            
            support_level = technical_indicators.get('support_level', current_price * 0.95)
            resistance_level = technical_indicators.get('resistance_level', current_price * 1.05)
            
            technical_data = {
                'ema_12': ema_12,
                'price_vs_ema12': (current_price - ema_12) / ema_12 if ema_12 > 0 else 0,
                'ema_26': ema_26,
                'price_vs_ema26': (current_price - ema_26) / ema_26 if ema_26 > 0 else 0,
                'sma_50': sma_50,
                'price_vs_sma50': (current_price - sma_50) / sma_50 if sma_50 > 0 else 0,
                'ema_cross_signal': 'Bullish' if ema_12 > ema_26 else 'Bearish',
                'rsi': rsi,
                'rsi_status': rsi_status,
                'macd': macd,
                'macd_signal': macd_signal,
                'macd_histogram': macd_histogram,
                'macd_status': macd_status,
                'atr': technical_indicators.get('atr', current_price * 0.02),
                'atr_percent': technical_indicators.get('atr_percent', 2.0) / 100,
                'bb_lower': bb_lower,
                'bb_upper': bb_upper,
                'bb_position': bb_position,
                'bb_width': bb_width,
                'volatility_regime': technical_indicators.get('volatility_regime', 'Medium'),
                'support_level': support_level,
                'resistance_level': resistance_level,
                'distance_to_support': (current_price - support_level) / current_price if current_price > 0 else 0,
                'distance_to_resistance': (resistance_level - current_price) / current_price if current_price > 0 else 0,
            }
            
            return self.technical_analysis_template.format(**technical_data)
            
        except Exception as e:
            logger.error(f"Error building technical context: {e}")
            return "TECHNICAL ANALYSIS: Error loading technical indicators\n"
    
    def _build_sentiment_context(self, sentiment_data: Dict[str, Any]) -> str:
        """Build sentiment analysis section."""
        try:
            sentiment_score = sentiment_data.get('compound', 0)
            
            # Determine overall sentiment
            if sentiment_score > 0.1:
                overall_sentiment = 'Bullish'
            elif sentiment_score < -0.1:
                overall_sentiment = 'Bearish'
            else:
                overall_sentiment = 'Neutral'
            
            sentiment_context_data = {
                'overall_sentiment': overall_sentiment,
                'sentiment_score': sentiment_score,
                'sentiment_confidence': abs(sentiment_score),
                'sentiment_positive': sentiment_data.get('positive', 0.33),
                'sentiment_neutral': sentiment_data.get('neutral', 0.33),
                'sentiment_negative': sentiment_data.get('negative', 0.33),
                'sentiment_trend_1h': sentiment_data.get('trend_1h', 'Stable'),
                'sentiment_trend_4h': sentiment_data.get('trend_4h', 'Stable'),
                'sentiment_volatility': sentiment_data.get('volatility', 0.1),
                'tweet_volume': sentiment_data.get('tweet_count', 100),
                'tweet_volume_ratio': sentiment_data.get('tweet_volume_ratio', 1.0),
                'engagement_rate': sentiment_data.get('engagement_rate', 0.05),
                'top_keywords': ', '.join(sentiment_data.get('top_keywords', ['bitcoin', 'crypto', 'trading']))
            }
            
            return self.sentiment_analysis_template.format(**sentiment_context_data)
            
        except Exception as e:
            logger.error(f"Error building sentiment context: {e}")
            return "SENTIMENT ANALYSIS: Error loading sentiment data\n"
    
    def _build_signals_context(self, recent_signals: List[Dict]) -> str:
        """Build recent signals context."""
        try:
            context = "\nRECENT TRADING SIGNALS:\n"
            context += "======================\n"
            
            for i, signal in enumerate(recent_signals[-5:]):  # Last 5 signals
                timestamp = signal.get('timestamp', 'Unknown')
                signal_type = signal.get('signal', 'HOLD')
                regime = signal.get('regime', 'Unknown')
                strength = signal.get('strength', 0.5)
                
                context += f"{i+1}. {timestamp}: {signal_type} ({regime}, strength: {strength:.1%})\n"
            
            return context + "\n"
            
        except Exception as e:
            logger.error(f"Error building signals context: {e}")
            return ""
    
    def _build_news_context(self, market_news: List[str]) -> str:
        """Build market news context."""
        try:
            context = "\nMARKET NEWS & CATALYSTS:\n"
            context += "========================\n"
            
            for i, news in enumerate(market_news[:5]):  # Top 5 news items
                context += f"{i+1}. {news}\n"
            
            return context + "\n"
            
        except Exception as e:
            logger.error(f"Error building news context: {e}")
            return ""
    
    def _build_final_request(self) -> str:
        """Build the final request section."""
        return """
TRADING RECOMMENDATION REQUEST:
==============================
Based on the comprehensive analysis above, please provide:

1. **TRADING RECOMMENDATION**: 
   - Clear action: BUY, SELL, or HOLD
   - Confidence level (1-10)
   - Position size recommendation

2. **ENTRY/EXIT STRATEGY**:
   - Specific entry price levels
   - Stop-loss recommendations
   - Take-profit targets
   - Time horizon

3. **RISK ASSESSMENT**:
   - Key risks to monitor
   - Maximum position size
   - Risk/reward ratio

4. **REASONING**:
   - Primary factors driving the recommendation
   - Technical vs fundamental weight
   - Market regime considerations

5. **MONITORING PLAN**:
   - Key levels to watch
   - Conditions that would change the thesis
   - Next review timeline

Please be specific, actionable, and include your confidence level in the analysis.
"""

    def _build_fallback_prompt(self, symbol: str, current_data: pd.Series) -> str:
        """Build a simple fallback prompt if main prompt fails."""
        current_price = current_data.get('close', 0)
        
        return f"""
You are a cryptocurrency trading analyst. 

Current Market Data:
- Symbol: {symbol}
- Price: ${current_price:,.2f}
- Volume: {current_data.get('volume', 0):,.0f}

Please provide a brief trading recommendation (BUY/SELL/HOLD) with reasoning.
"""

    def build_quick_analysis_prompt(
        self, 
        symbol: str, 
        current_price: float, 
        signal: TradingSignal, 
        regime: str,
        confidence: float
    ) -> str:
        """Build a quick analysis prompt for simple recommendations."""
        signal_name = signal.name if hasattr(signal, 'name') else str(signal)
        
        return f"""
You are a crypto trading analyst. Provide a concise analysis:

Market Data:
- Symbol: {symbol}
- Current Price: ${current_price:,.2f}
- HMM Signal: {signal_name}
- Market Regime: {regime}
- Signal Confidence: {confidence:.1%}

Provide a brief recommendation with:
1. Action (BUY/SELL/HOLD)
2. Key reasoning (2-3 sentences)
3. Risk level (Low/Medium/High)
4. Time horizon (Short/Medium/Long term)

Keep response under 150 words.
"""
