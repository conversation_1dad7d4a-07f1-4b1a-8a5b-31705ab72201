"""
Context analyzer for gathering and processing market context for LLM analysis.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
import requests
from loguru import logger

from ..config import settings


class ContextAnalyzer:
    """Analyze and gather market context for LLM recommendations."""
    
    def __init__(self):
        self.news_sources = [
            'https://api.coindesk.com/v1/news',
            'https://api.cointelegraph.com/v1/news',
            # Add more news sources as needed
        ]
        self.context_cache = {}
        self.cache_duration = timedelta(minutes=15)  # Cache context for 15 minutes
        
    def gather_comprehensive_context(
        self,
        symbol: str,
        features_df: pd.DataFrame,
        regime_info: Dict[str, Any],
        sentiment_data: Optional[Dict[str, Any]] = None,
        include_news: bool = True
    ) -> Dict[str, Any]:
        """
        Gather comprehensive market context for LLM analysis.
        
        Args:
            symbol: Trading symbol
            features_df: DataFrame with features and indicators
            regime_info: Current regime information
            sentiment_data: Sentiment analysis data
            include_news: Whether to include news data
            
        Returns:
            Comprehensive context dictionary
        """
        try:
            logger.info(f"Gathering comprehensive context for {symbol}")
            
            # Check cache first
            cache_key = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M')}"
            if cache_key in self.context_cache:
                cached_time, cached_data = self.context_cache[cache_key]
                if datetime.now() - cached_time < self.cache_duration:
                    logger.info("Using cached context data")
                    return cached_data
            
            context = {}
            
            # Current market data
            if not features_df.empty:
                current_data = features_df.iloc[-1]
                context['current_data'] = current_data
                context['market_summary'] = self._analyze_market_summary(features_df)
            
            # Technical analysis context
            context['technical_analysis'] = self._analyze_technical_indicators(features_df)
            
            # Regime analysis context
            context['regime_analysis'] = self._analyze_regime_context(regime_info, features_df)
            
            # Sentiment context
            if sentiment_data:
                context['sentiment_analysis'] = self._analyze_sentiment_context(sentiment_data)
            
            # Market structure analysis
            context['market_structure'] = self._analyze_market_structure(features_df)
            
            # Volatility and risk context
            context['risk_analysis'] = self._analyze_risk_context(features_df)
            
            # News and fundamental context
            if include_news:
                context['news_analysis'] = self._gather_news_context(symbol)
            
            # Recent performance context
            context['performance_context'] = self._analyze_recent_performance(features_df)
            
            # Cache the result
            self.context_cache[cache_key] = (datetime.now(), context)
            
            logger.info("Context gathering completed successfully")
            return context
            
        except Exception as e:
            logger.error(f"Error gathering comprehensive context: {e}")
            return {}
    
    def _analyze_market_summary(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze overall market summary."""
        try:
            if features_df.empty:
                return {}
            
            current = features_df.iloc[-1]
            
            # Price analysis
            price_changes = {}
            for periods, label in [(20, '1h'), (80, '4h'), (480, '24h'), (3360, '7d')]:
                if len(features_df) > periods:
                    old_price = features_df.iloc[-periods]['close']
                    current_price = current['close']
                    price_changes[label] = (current_price - old_price) / old_price
            
            # Volume analysis
            volume_analysis = {}
            if 'volume' in features_df.columns:
                current_volume = current['volume']
                avg_volume = features_df['volume'].tail(100).mean()
                volume_analysis = {
                    'current_volume': current_volume,
                    'average_volume': avg_volume,
                    'volume_ratio': current_volume / avg_volume if avg_volume > 0 else 1.0,
                    'volume_trend': self._calculate_trend(features_df['volume'].tail(20))
                }
            
            return {
                'current_price': current['close'],
                'price_changes': price_changes,
                'volume_analysis': volume_analysis,
                'trading_range': {
                    'high_24h': features_df['high'].tail(480).max() if len(features_df) > 480 else current['high'],
                    'low_24h': features_df['low'].tail(480).min() if len(features_df) > 480 else current['low'],
                }
            }
            
        except Exception as e:
            logger.error(f"Error analyzing market summary: {e}")
            return {}
    
    def _analyze_technical_indicators(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze technical indicators context."""
        try:
            if features_df.empty:
                return {}
            
            current = features_df.iloc[-1]
            
            # Trend analysis
            trend_indicators = {}
            for indicator in ['ema_12', 'ema_26', 'sma_20', 'sma_50']:
                if indicator in features_df.columns:
                    value = current[indicator]
                    trend = self._calculate_trend(features_df[indicator].tail(10))
                    trend_indicators[indicator] = {
                        'value': value,
                        'trend': trend,
                        'vs_price': (current['close'] - value) / value if value > 0 else 0
                    }
            
            # Momentum analysis
            momentum_indicators = {}
            for indicator in ['rsi', 'macd', 'macd_histogram', 'stoch_k']:
                if indicator in features_df.columns:
                    value = current[indicator]
                    trend = self._calculate_trend(features_df[indicator].tail(10))
                    momentum_indicators[indicator] = {
                        'value': value,
                        'trend': trend,
                        'status': self._interpret_momentum_indicator(indicator, value)
                    }
            
            # Volatility analysis
            volatility_indicators = {}
            for indicator in ['atr', 'bb_width', 'price_volatility_20']:
                if indicator in features_df.columns:
                    value = current[indicator]
                    avg_value = features_df[indicator].tail(50).mean()
                    volatility_indicators[indicator] = {
                        'value': value,
                        'vs_average': (value - avg_value) / avg_value if avg_value > 0 else 0,
                        'regime': 'High' if value > avg_value * 1.5 else 'Low' if value < avg_value * 0.5 else 'Normal'
                    }
            
            return {
                'trend_indicators': trend_indicators,
                'momentum_indicators': momentum_indicators,
                'volatility_indicators': volatility_indicators,
                'overall_technical_bias': self._calculate_technical_bias(current)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing technical indicators: {e}")
            return {}
    
    def _analyze_regime_context(
        self, 
        regime_info: Dict[str, Any], 
        features_df: pd.DataFrame
    ) -> Dict[str, Any]:
        """Analyze regime context and transitions."""
        try:
            context = {
                'current_regime': regime_info.get('regime', 'Unknown'),
                'regime_confidence': regime_info.get('confidence', 0.5),
                'regime_probabilities': regime_info.get('probabilities', {}),
            }
            
            # Analyze regime stability
            if not features_df.empty and len(features_df) > 20:
                recent_regimes = []
                # This would normally come from regime predictions
                # For now, we'll simulate regime stability analysis
                context['regime_stability'] = {
                    'recent_changes': 0,  # Number of regime changes in last 20 periods
                    'stability_score': 0.8,  # How stable the current regime is
                    'time_in_regime': 50,  # Periods in current regime
                }
            
            # Regime transition analysis
            context['transition_analysis'] = {
                'likely_next_regime': 'Bull Market',  # Based on transition probabilities
                'transition_probability': 0.3,
                'key_transition_triggers': ['Volume spike', 'Technical breakout', 'Sentiment shift']
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing regime context: {e}")
            return {}
    
    def _analyze_sentiment_context(self, sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment context and trends."""
        try:
            context = {
                'current_sentiment': sentiment_data.get('compound', 0),
                'sentiment_distribution': {
                    'positive': sentiment_data.get('positive', 0.33),
                    'neutral': sentiment_data.get('neutral', 0.33),
                    'negative': sentiment_data.get('negative', 0.33)
                },
                'sentiment_strength': abs(sentiment_data.get('compound', 0)),
                'sentiment_label': self._interpret_sentiment_score(sentiment_data.get('compound', 0))
            }
            
            # Sentiment trend analysis
            context['sentiment_trends'] = {
                'short_term_trend': sentiment_data.get('trend_1h', 'Stable'),
                'medium_term_trend': sentiment_data.get('trend_4h', 'Stable'),
                'volatility': sentiment_data.get('volatility', 0.1),
                'momentum': sentiment_data.get('momentum', 'Neutral')
            }
            
            # Social metrics
            context['social_metrics'] = {
                'tweet_volume': sentiment_data.get('tweet_count', 0),
                'engagement_rate': sentiment_data.get('engagement_rate', 0),
                'top_keywords': sentiment_data.get('top_keywords', []),
                'influencer_sentiment': sentiment_data.get('influencer_sentiment', 'Neutral')
            }
            
            return context
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment context: {e}")
            return {}
    
    def _analyze_market_structure(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market structure and key levels."""
        try:
            if features_df.empty:
                return {}
            
            current_price = features_df.iloc[-1]['close']
            
            # Support and resistance levels
            highs = features_df['high'].tail(100)
            lows = features_df['low'].tail(100)
            
            # Simple support/resistance calculation
            resistance_levels = []
            support_levels = []
            
            # Find recent highs and lows
            for i in range(5, len(highs) - 5):
                if highs.iloc[i] == highs.iloc[i-5:i+6].max():
                    resistance_levels.append(highs.iloc[i])
                if lows.iloc[i] == lows.iloc[i-5:i+6].min():
                    support_levels.append(lows.iloc[i])
            
            # Get closest levels
            resistance_levels = sorted(resistance_levels, reverse=True)
            support_levels = sorted(support_levels, reverse=True)
            
            nearest_resistance = next((r for r in resistance_levels if r > current_price), None)
            nearest_support = next((s for s in support_levels if s < current_price), None)
            
            return {
                'support_resistance': {
                    'nearest_support': nearest_support,
                    'nearest_resistance': nearest_resistance,
                    'support_strength': len([s for s in support_levels if abs(s - nearest_support) < current_price * 0.01]) if nearest_support else 0,
                    'resistance_strength': len([r for r in resistance_levels if abs(r - nearest_resistance) < current_price * 0.01]) if nearest_resistance else 0
                },
                'price_position': {
                    'distance_to_support': (current_price - nearest_support) / current_price if nearest_support else 0,
                    'distance_to_resistance': (nearest_resistance - current_price) / current_price if nearest_resistance else 0
                },
                'market_structure_bias': 'Bullish' if current_price > features_df['close'].tail(20).mean() else 'Bearish'
            }
            
        except Exception as e:
            logger.error(f"Error analyzing market structure: {e}")
            return {}
    
    def _analyze_risk_context(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze risk and volatility context."""
        try:
            if features_df.empty:
                return {}
            
            # Calculate returns
            returns = features_df['close'].pct_change().dropna()
            
            # Risk metrics
            risk_metrics = {
                'volatility': {
                    'daily': returns.std() if len(returns) > 0 else 0,
                    'weekly': returns.tail(7*24*60).std() if len(returns) > 7*24*60 else 0,
                    'regime': 'High' if returns.std() > 0.03 else 'Low' if returns.std() < 0.01 else 'Medium'
                },
                'drawdown': {
                    'current': self._calculate_current_drawdown(features_df['close']),
                    'max_recent': self._calculate_max_drawdown(features_df['close'].tail(1000))
                },
                'var_estimates': {
                    'var_95': np.percentile(returns, 5) if len(returns) > 20 else 0,
                    'var_99': np.percentile(returns, 1) if len(returns) > 20 else 0
                }
            }
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error analyzing risk context: {e}")
            return {}
    
    def _gather_news_context(self, symbol: str) -> Dict[str, Any]:
        """Gather relevant news and fundamental context."""
        try:
            # This is a simplified implementation
            # In production, you'd integrate with real news APIs
            
            mock_news = [
                f"{symbol} shows strong technical breakout above key resistance",
                "Institutional adoption of cryptocurrency continues to grow",
                "Regulatory clarity improves market sentiment",
                "Major exchange announces new trading features",
                "Market volatility decreases as institutional interest grows"
            ]
            
            return {
                'recent_headlines': mock_news[:3],
                'sentiment_from_news': 'Positive',
                'key_themes': ['Institutional adoption', 'Technical breakout', 'Regulatory clarity'],
                'news_impact_score': 0.6,  # 0-1 scale
                'fundamental_outlook': 'Bullish'
            }
            
        except Exception as e:
            logger.error(f"Error gathering news context: {e}")
            return {}
    
    def _analyze_recent_performance(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze recent performance context."""
        try:
            if features_df.empty or len(features_df) < 100:
                return {}
            
            current_price = features_df.iloc[-1]['close']
            
            # Performance over different periods
            performance = {}
            for periods, label in [(60, '1h'), (240, '4h'), (1440, '24h'), (10080, '7d')]:
                if len(features_df) > periods:
                    old_price = features_df.iloc[-periods]['close']
                    performance[label] = (current_price - old_price) / old_price
            
            # Relative performance metrics
            recent_high = features_df['high'].tail(1440).max()  # 24h high
            recent_low = features_df['low'].tail(1440).min()    # 24h low
            
            return {
                'performance': performance,
                'position_in_range': (current_price - recent_low) / (recent_high - recent_low) if recent_high > recent_low else 0.5,
                'momentum': 'Strong' if performance.get('4h', 0) > 0.02 else 'Weak' if performance.get('4h', 0) < -0.02 else 'Neutral',
                'consistency': self._calculate_performance_consistency(features_df['close'].tail(100))
            }
            
        except Exception as e:
            logger.error(f"Error analyzing recent performance: {e}")
            return {}
    
    def _calculate_trend(self, series: pd.Series) -> str:
        """Calculate trend direction for a series."""
        if len(series) < 2:
            return 'Neutral'
        
        slope = (series.iloc[-1] - series.iloc[0]) / len(series)
        if slope > series.std() * 0.1:
            return 'Rising'
        elif slope < -series.std() * 0.1:
            return 'Falling'
        else:
            return 'Sideways'
    
    def _interpret_momentum_indicator(self, indicator: str, value: float) -> str:
        """Interpret momentum indicator values."""
        if indicator == 'rsi':
            if value > 70:
                return 'Overbought'
            elif value < 30:
                return 'Oversold'
            else:
                return 'Neutral'
        elif indicator == 'macd_histogram':
            if value > 0:
                return 'Bullish'
            else:
                return 'Bearish'
        else:
            return 'Neutral'
    
    def _calculate_technical_bias(self, current_data: pd.Series) -> str:
        """Calculate overall technical bias."""
        bullish_signals = 0
        bearish_signals = 0
        
        # EMA signals
        if 'ema_12' in current_data and 'ema_26' in current_data:
            if current_data['ema_12'] > current_data['ema_26']:
                bullish_signals += 1
            else:
                bearish_signals += 1
        
        # RSI signals
        if 'rsi' in current_data:
            rsi = current_data['rsi']
            if 30 < rsi < 70:
                if rsi > 50:
                    bullish_signals += 0.5
                else:
                    bearish_signals += 0.5
        
        # MACD signals
        if 'macd' in current_data and 'macd_signal' in current_data:
            if current_data['macd'] > current_data['macd_signal']:
                bullish_signals += 1
            else:
                bearish_signals += 1
        
        if bullish_signals > bearish_signals:
            return 'Bullish'
        elif bearish_signals > bullish_signals:
            return 'Bearish'
        else:
            return 'Neutral'
    
    def _interpret_sentiment_score(self, score: float) -> str:
        """Interpret sentiment score."""
        if score > 0.1:
            return 'Bullish'
        elif score < -0.1:
            return 'Bearish'
        else:
            return 'Neutral'
    
    def _calculate_current_drawdown(self, prices: pd.Series) -> float:
        """Calculate current drawdown from recent peak."""
        if len(prices) == 0:
            return 0
        
        peak = prices.expanding().max()
        drawdown = (prices - peak) / peak
        return drawdown.iloc[-1]
    
    def _calculate_max_drawdown(self, prices: pd.Series) -> float:
        """Calculate maximum drawdown."""
        if len(prices) == 0:
            return 0
        
        peak = prices.expanding().max()
        drawdown = (prices - peak) / peak
        return drawdown.min()
    
    def _calculate_performance_consistency(self, prices: pd.Series) -> str:
        """Calculate performance consistency."""
        if len(prices) < 10:
            return 'Unknown'
        
        returns = prices.pct_change().dropna()
        positive_days = (returns > 0).sum()
        total_days = len(returns)
        
        consistency_ratio = positive_days / total_days if total_days > 0 else 0
        
        if consistency_ratio > 0.6:
            return 'High'
        elif consistency_ratio > 0.4:
            return 'Medium'
        else:
            return 'Low'
