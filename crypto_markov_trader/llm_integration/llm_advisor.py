"""
LLM-powered trading advisor for contextual recommendations.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
import json
import re
from loguru import logger

try:
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
    import torch
    TRANSFORMERS_AVAILABLE = True
except ImportError:
    TRANSFORMERS_AVAILABLE = False
    logger.warning("Transformers not available. LLM features will be limited.")

from .prompt_builder import PromptBuilder
from .context_analyzer import ContextAnalyzer
from ..models import TradingSignal
from ..config import settings


class LLMTradingAdvisor:
    """LLM-powered trading advisor for contextual analysis and recommendations."""
    
    def __init__(self, model_name: Optional[str] = None, use_local_model: bool = True):
        """
        Initialize LLM trading advisor.
        
        Args:
            model_name: Name of the LLM model to use
            use_local_model: Whether to use local model or API
        """
        self.model_name = model_name or settings.llm_model_name
        self.use_local_model = use_local_model
        self.prompt_builder = PromptBuilder()
        self.context_analyzer = ContextAnalyzer()
        
        # Initialize LLM components
        self.tokenizer = None
        self.model = None
        self.pipeline = None
        self.device = "cuda" if torch.cuda.is_available() and TRANSFORMERS_AVAILABLE else "cpu"
        
        if TRANSFORMERS_AVAILABLE and use_local_model:
            self._initialize_local_model()
        else:
            logger.info("Using fallback analysis mode (no LLM)")
    
    def _initialize_local_model(self) -> None:
        """Initialize local LLM model."""
        try:
            logger.info(f"Initializing LLM model: {self.model_name}")
            
            # Use a smaller, efficient model for trading analysis
            # In production, you might want to use larger models or API services
            model_name = "microsoft/DialoGPT-medium"  # Fallback to available model
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.model = AutoModelForCausalLM.from_pretrained(model_name)
            
            # Add padding token if not present
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
            
            # Create text generation pipeline
            self.pipeline = pipeline(
                "text-generation",
                model=self.model,
                tokenizer=self.tokenizer,
                device=0 if self.device == "cuda" else -1,
                max_length=512,
                do_sample=True,
                temperature=0.7,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            logger.info(f"LLM model initialized successfully on {self.device}")
            
        except Exception as e:
            logger.error(f"Error initializing LLM model: {e}")
            self.pipeline = None
    
    def generate_trading_recommendation(
        self,
        symbol: str,
        features_df: pd.DataFrame,
        regime_info: Dict[str, Any],
        sentiment_data: Optional[Dict[str, Any]] = None,
        recent_signals: Optional[List[Dict]] = None,
        include_detailed_analysis: bool = True
    ) -> Dict[str, Any]:
        """
        Generate comprehensive trading recommendation using LLM analysis.
        
        Args:
            symbol: Trading symbol
            features_df: DataFrame with features and indicators
            regime_info: Current regime information
            sentiment_data: Sentiment analysis data
            recent_signals: Recent trading signals
            include_detailed_analysis: Whether to include detailed analysis
            
        Returns:
            Dictionary with trading recommendation and analysis
        """
        try:
            logger.info(f"Generating LLM trading recommendation for {symbol}")
            
            if features_df.empty:
                return {"error": "No market data available"}
            
            # Gather comprehensive context
            context = self.context_analyzer.gather_comprehensive_context(
                symbol, features_df, regime_info, sentiment_data
            )
            
            if include_detailed_analysis and self.pipeline:
                # Generate detailed LLM analysis
                recommendation = self._generate_detailed_recommendation(
                    symbol, features_df, context, recent_signals
                )
            else:
                # Generate rule-based recommendation
                recommendation = self._generate_rule_based_recommendation(
                    symbol, features_df, context, regime_info
                )
            
            # Add metadata
            recommendation['metadata'] = {
                'timestamp': datetime.now(),
                'symbol': symbol,
                'analysis_type': 'LLM' if self.pipeline else 'Rule-based',
                'model_name': self.model_name if self.pipeline else 'Rule-based',
                'context_quality': self._assess_context_quality(context)
            }
            
            logger.info("Trading recommendation generated successfully")
            return recommendation
            
        except Exception as e:
            logger.error(f"Error generating trading recommendation: {e}")
            return {"error": str(e)}
    
    def _generate_detailed_recommendation(
        self,
        symbol: str,
        features_df: pd.DataFrame,
        context: Dict[str, Any],
        recent_signals: Optional[List[Dict]] = None
    ) -> Dict[str, Any]:
        """Generate detailed recommendation using LLM."""
        try:
            # Build comprehensive prompt
            current_data = features_df.iloc[-1]
            regime_info = context.get('regime_analysis', {})
            technical_indicators = self._extract_technical_indicators(current_data)
            sentiment_data = context.get('sentiment_analysis', {})
            
            prompt = self.prompt_builder.build_comprehensive_prompt(
                symbol=symbol,
                current_data=current_data,
                regime_info=regime_info,
                technical_indicators=technical_indicators,
                sentiment_data=sentiment_data,
                recent_signals=recent_signals
            )
            
            # Generate LLM response
            llm_response = self._query_llm(prompt)
            
            # Parse LLM response
            parsed_recommendation = self._parse_llm_response(llm_response)
            
            # Add context analysis
            parsed_recommendation['context_analysis'] = self._summarize_context(context)
            
            return parsed_recommendation
            
        except Exception as e:
            logger.error(f"Error generating detailed recommendation: {e}")
            return self._generate_rule_based_recommendation(symbol, features_df, context, {})
    
    def _generate_rule_based_recommendation(
        self,
        symbol: str,
        features_df: pd.DataFrame,
        context: Dict[str, Any],
        regime_info: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate rule-based recommendation as fallback."""
        try:
            current_data = features_df.iloc[-1]
            current_price = current_data['close']
            
            # Analyze technical signals
            technical_score = self._calculate_technical_score(current_data)
            
            # Analyze regime signal
            regime_score = self._calculate_regime_score(regime_info)
            
            # Analyze sentiment signal
            sentiment_analysis = context.get('sentiment_analysis', {})
            sentiment_score = sentiment_analysis.get('current_sentiment', 0)
            
            # Combine signals
            combined_score = (technical_score * 0.4 + regime_score * 0.4 + sentiment_score * 0.2)
            
            # Generate recommendation
            if combined_score > 0.3:
                action = "BUY"
                confidence = min(combined_score * 2, 1.0)
            elif combined_score < -0.3:
                action = "SELL"
                confidence = min(abs(combined_score) * 2, 1.0)
            else:
                action = "HOLD"
                confidence = 0.5
            
            # Calculate position size
            risk_level = self._assess_risk_level(context)
            position_size = self._calculate_position_size(confidence, risk_level)
            
            # Generate price targets
            targets = self._calculate_price_targets(current_price, action, context)
            
            return {
                'recommendation': {
                    'action': action,
                    'confidence': confidence,
                    'position_size': position_size,
                    'risk_level': risk_level
                },
                'price_targets': targets,
                'reasoning': {
                    'technical_score': technical_score,
                    'regime_score': regime_score,
                    'sentiment_score': sentiment_score,
                    'combined_score': combined_score,
                    'key_factors': self._identify_key_factors(context)
                },
                'risk_assessment': self._assess_risks(context),
                'monitoring_plan': self._create_monitoring_plan(current_price, action, context)
            }
            
        except Exception as e:
            logger.error(f"Error generating rule-based recommendation: {e}")
            return {"error": str(e)}
    
    def _query_llm(self, prompt: str, max_length: int = 512) -> str:
        """Query the LLM with the given prompt."""
        try:
            if not self.pipeline:
                return "LLM not available"
            
            # Truncate prompt if too long
            max_prompt_length = 400  # Leave room for response
            if len(prompt) > max_prompt_length:
                prompt = prompt[:max_prompt_length] + "..."
            
            # Generate response
            response = self.pipeline(
                prompt,
                max_length=max_length,
                num_return_sequences=1,
                temperature=0.7,
                do_sample=True,
                pad_token_id=self.tokenizer.eos_token_id
            )
            
            if response and len(response) > 0:
                generated_text = response[0]['generated_text']
                # Extract only the new text (remove the prompt)
                new_text = generated_text[len(prompt):].strip()
                return new_text if new_text else "No response generated"
            else:
                return "No response generated"
                
        except Exception as e:
            logger.error(f"Error querying LLM: {e}")
            return f"Error generating response: {e}"
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """Parse LLM response into structured recommendation."""
        try:
            # This is a simplified parser
            # In production, you'd want more sophisticated parsing
            
            recommendation = {
                'recommendation': {
                    'action': 'HOLD',
                    'confidence': 0.5,
                    'position_size': 0.02,
                    'risk_level': 'Medium'
                },
                'reasoning': {
                    'llm_analysis': response,
                    'key_points': self._extract_key_points(response)
                }
            }
            
            # Try to extract action from response
            response_lower = response.lower()
            if 'buy' in response_lower and 'sell' not in response_lower:
                recommendation['recommendation']['action'] = 'BUY'
                recommendation['recommendation']['confidence'] = 0.7
            elif 'sell' in response_lower and 'buy' not in response_lower:
                recommendation['recommendation']['action'] = 'SELL'
                recommendation['recommendation']['confidence'] = 0.7
            
            return recommendation
            
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}")
            return {"error": str(e)}
    
    def _extract_technical_indicators(self, current_data: pd.Series) -> Dict[str, float]:
        """Extract technical indicators from current data."""
        indicators = {}
        
        # Common technical indicators
        for indicator in ['ema_12', 'ema_26', 'sma_20', 'sma_50', 'rsi', 'macd', 
                         'macd_signal', 'macd_histogram', 'bb_upper', 'bb_lower', 
                         'atr', 'volume_ratio']:
            if indicator in current_data:
                indicators[indicator] = current_data[indicator]
        
        return indicators
    
    def _calculate_technical_score(self, current_data: pd.Series) -> float:
        """Calculate technical analysis score."""
        score = 0.0
        signals = 0
        
        # EMA crossover
        if 'ema_12' in current_data and 'ema_26' in current_data:
            if current_data['ema_12'] > current_data['ema_26']:
                score += 0.3
            else:
                score -= 0.3
            signals += 1
        
        # RSI
        if 'rsi' in current_data:
            rsi = current_data['rsi']
            if rsi < 30:
                score += 0.2  # Oversold, potential buy
            elif rsi > 70:
                score -= 0.2  # Overbought, potential sell
            signals += 1
        
        # MACD
        if 'macd' in current_data and 'macd_signal' in current_data:
            if current_data['macd'] > current_data['macd_signal']:
                score += 0.2
            else:
                score -= 0.2
            signals += 1
        
        return score / max(signals, 1)
    
    def _calculate_regime_score(self, regime_info: Dict[str, Any]) -> float:
        """Calculate regime-based score."""
        regime = regime_info.get('current_regime', 'Unknown')
        confidence = regime_info.get('regime_confidence', 0.5)
        
        if 'Bull' in regime:
            return confidence * 0.5
        elif 'Bear' in regime:
            return -confidence * 0.5
        else:
            return 0.0
    
    def _assess_risk_level(self, context: Dict[str, Any]) -> str:
        """Assess overall risk level."""
        risk_analysis = context.get('risk_analysis', {})
        volatility = risk_analysis.get('volatility', {}).get('daily', 0.02)
        
        if volatility > 0.05:
            return 'High'
        elif volatility > 0.02:
            return 'Medium'
        else:
            return 'Low'
    
    def _calculate_position_size(self, confidence: float, risk_level: str) -> float:
        """Calculate recommended position size."""
        base_size = 0.02  # 2% base position
        
        # Adjust for confidence
        size = base_size * confidence
        
        # Adjust for risk
        if risk_level == 'High':
            size *= 0.5
        elif risk_level == 'Low':
            size *= 1.5
        
        return min(size, 0.05)  # Cap at 5%
    
    def _calculate_price_targets(
        self, 
        current_price: float, 
        action: str, 
        context: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate price targets."""
        market_structure = context.get('market_structure', {})
        support_resistance = market_structure.get('support_resistance', {})
        
        if action == 'BUY':
            return {
                'entry': current_price,
                'stop_loss': support_resistance.get('nearest_support', current_price * 0.95),
                'take_profit': support_resistance.get('nearest_resistance', current_price * 1.05)
            }
        elif action == 'SELL':
            return {
                'entry': current_price,
                'stop_loss': support_resistance.get('nearest_resistance', current_price * 1.05),
                'take_profit': support_resistance.get('nearest_support', current_price * 0.95)
            }
        else:
            return {
                'entry': current_price,
                'stop_loss': current_price * 0.98,
                'take_profit': current_price * 1.02
            }
    
    def _identify_key_factors(self, context: Dict[str, Any]) -> List[str]:
        """Identify key factors driving the recommendation."""
        factors = []
        
        # Technical factors
        technical = context.get('technical_analysis', {})
        if technical.get('overall_technical_bias') == 'Bullish':
            factors.append("Technical indicators show bullish bias")
        elif technical.get('overall_technical_bias') == 'Bearish':
            factors.append("Technical indicators show bearish bias")
        
        # Regime factors
        regime = context.get('regime_analysis', {})
        current_regime = regime.get('current_regime', '')
        if current_regime:
            factors.append(f"Market regime: {current_regime}")
        
        # Sentiment factors
        sentiment = context.get('sentiment_analysis', {})
        sentiment_label = sentiment.get('sentiment_label', '')
        if sentiment_label:
            factors.append(f"Market sentiment: {sentiment_label}")
        
        return factors
    
    def _assess_risks(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Assess key risks."""
        risk_analysis = context.get('risk_analysis', {})
        
        return {
            'volatility_risk': risk_analysis.get('volatility', {}).get('regime', 'Medium'),
            'drawdown_risk': 'High' if risk_analysis.get('drawdown', {}).get('current', 0) < -0.1 else 'Low',
            'regime_transition_risk': 'Medium',  # Based on regime stability
            'key_risk_factors': [
                'Market volatility',
                'Regime transition',
                'Sentiment shift'
            ]
        }
    
    def _create_monitoring_plan(
        self, 
        current_price: float, 
        action: str, 
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create monitoring plan."""
        return {
            'key_levels_to_watch': [
                current_price * 0.98,  # Support
                current_price * 1.02   # Resistance
            ],
            'indicators_to_monitor': ['RSI', 'MACD', 'Volume'],
            'review_frequency': '4 hours',
            'exit_conditions': [
                'Stop loss hit',
                'Take profit reached',
                'Regime change confirmed'
            ]
        }
    
    def _summarize_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize context for the recommendation."""
        return {
            'market_summary': context.get('market_summary', {}),
            'technical_bias': context.get('technical_analysis', {}).get('overall_technical_bias', 'Neutral'),
            'regime': context.get('regime_analysis', {}).get('current_regime', 'Unknown'),
            'sentiment': context.get('sentiment_analysis', {}).get('sentiment_label', 'Neutral'),
            'risk_level': self._assess_risk_level(context)
        }
    
    def _assess_context_quality(self, context: Dict[str, Any]) -> str:
        """Assess the quality of gathered context."""
        quality_score = 0
        
        if context.get('market_summary'):
            quality_score += 1
        if context.get('technical_analysis'):
            quality_score += 1
        if context.get('regime_analysis'):
            quality_score += 1
        if context.get('sentiment_analysis'):
            quality_score += 1
        if context.get('risk_analysis'):
            quality_score += 1
        
        if quality_score >= 4:
            return 'High'
        elif quality_score >= 3:
            return 'Medium'
        else:
            return 'Low'
    
    def _extract_key_points(self, text: str) -> List[str]:
        """Extract key points from LLM response."""
        # Simple extraction based on common patterns
        points = []
        
        # Look for numbered points
        numbered_points = re.findall(r'\d+\.\s*([^.]+)', text)
        points.extend(numbered_points[:3])  # Take first 3
        
        # Look for bullet points
        bullet_points = re.findall(r'[-*]\s*([^.]+)', text)
        points.extend(bullet_points[:3])  # Take first 3
        
        return points[:5]  # Return max 5 points
    
    def generate_quick_recommendation(
        self,
        symbol: str,
        current_price: float,
        signal: TradingSignal,
        regime: str,
        confidence: float
    ) -> Dict[str, Any]:
        """Generate quick recommendation for immediate use."""
        try:
            if self.pipeline:
                # Use LLM for quick analysis
                prompt = self.prompt_builder.build_quick_analysis_prompt(
                    symbol, current_price, signal, regime, confidence
                )
                
                llm_response = self._query_llm(prompt, max_length=200)
                
                return {
                    'recommendation': signal.name if hasattr(signal, 'name') else str(signal),
                    'confidence': confidence,
                    'analysis': llm_response,
                    'timestamp': datetime.now()
                }
            else:
                # Rule-based quick recommendation
                return {
                    'recommendation': signal.name if hasattr(signal, 'name') else str(signal),
                    'confidence': confidence,
                    'analysis': f"Based on {regime} regime with {confidence:.1%} confidence",
                    'timestamp': datetime.now()
                }
                
        except Exception as e:
            logger.error(f"Error generating quick recommendation: {e}")
            return {"error": str(e)}
    
    def is_available(self) -> bool:
        """Check if LLM advisor is available."""
        return TRANSFORMERS_AVAILABLE and self.pipeline is not None
