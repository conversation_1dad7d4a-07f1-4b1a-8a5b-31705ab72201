"""
Crypto Markov Trader - A regime-based trading system using Hidden Markov Models and sentiment analysis.

This package implements a comprehensive crypto trading system that combines:
- Technical analysis using pandas-ta
- Hidden Markov Models for regime identification using hmmlearn
- Twitter sentiment analysis using Hugging Face Transformers
- Real-time data collection from Hyperliquid
- Backtesting and evaluation framework
"""

__version__ = "0.1.0"
__author__ = "Crypto Markov Trader"

from .config import Settings

__all__ = ["Settings"]
