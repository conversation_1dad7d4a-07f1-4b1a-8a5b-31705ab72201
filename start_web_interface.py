#!/usr/bin/env python3
"""
Quick start script for the Crypto Markov Trader Web Interface.
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """Start the web interface."""
    print("🚀 Starting Crypto Markov Trader Web Interface")
    print("=" * 50)
    
    # Check if Flask is installed
    try:
        import flask
        print("✅ Flask is installed")
    except ImportError:
        print("❌ Flask not found. Installing...")
        subprocess.run([sys.executable, "-m", "pip", "install", "flask"])
        print("✅ Flask installed")
    
    # Create directories if they don't exist
    os.makedirs("web_interface/templates", exist_ok=True)
    os.makedirs("web_interface/static", exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data/twitter_cache", exist_ok=True)
    
    print("✅ Directories created")
    
    # Start the web interface
    print("\n🌐 Starting web server...")
    print("📊 Dashboard will be available at: http://localhost:5000")
    print("⚠️  Press Ctrl+C to stop the server")
    print("=" * 50)
    
    try:
        # Change to web_interface directory and run the app
        os.chdir("web_interface")
        subprocess.run([sys.executable, "app.py"])
    except KeyboardInterrupt:
        print("\n🛑 Web interface stopped")
    except Exception as e:
        print(f"❌ Error starting web interface: {e}")
        print("\nTry running manually:")
        print("cd web_interface && python app.py")

if __name__ == "__main__":
    main()
