#!/usr/bin/env python3
"""
Main entry point for the Crypto Markov Trader system.
"""

import asyncio
import argparse
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger

from crypto_markov_trader.config import settings
from crypto_markov_trader.utils import setup_logging
from crypto_markov_trader.data_collection import HyperliquidClient, TwitterClient, DataAggregator
from crypto_markov_trader.feature_engineering import FeaturePipeline
from crypto_markov_trader.models import ModelTrainer, MarkovRegimeModel, RegimeClassifier
from crypto_markov_trader.backtesting import BacktestEngine, PerformanceMetrics, StrategyOptimizer
from crypto_markov_trader.llm_integration import LLMTradingAdvisor
from crypto_markov_trader.trading import LiveTrader, TradingMonitor


def generate_demo_market_data(symbol: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
    """Generate realistic demo market data for testing."""
    try:
        import numpy as np

        # Calculate number of minutes
        duration = end_time - start_time
        num_minutes = int(duration.total_seconds() / 60)

        if num_minutes <= 0:
            return pd.DataFrame()

        # Generate timestamps
        timestamps = pd.date_range(start=start_time, end=end_time, freq='1min')[:num_minutes]

        # Base prices for different symbols
        base_prices = {
            'BTC': 95000,
            'ETH': 3500,
            'SOL': 200,
            'HYPE': 25
        }

        base_price = base_prices.get(symbol, 50000)

        # Generate realistic price movements
        np.random.seed(42)  # For reproducible demo data
        returns = np.random.normal(0, 0.002, num_minutes)  # 0.2% volatility

        prices = [base_price]
        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        # Ensure we have the right number of prices
        prices = prices[:num_minutes]

        # Generate OHLCV data
        data = []
        for i, (timestamp, close_price) in enumerate(zip(timestamps, prices)):
            # Generate realistic OHLC from close price
            volatility = abs(np.random.normal(0, 0.001))
            high = close_price * (1 + volatility)
            low = close_price * (1 - volatility)
            open_price = prices[i-1] if i > 0 else close_price

            # Generate volume
            volume = np.random.uniform(100, 1000)

            data.append({
                'timestamp': timestamp,
                'open': open_price,
                'high': high,
                'low': low,
                'close': close_price,
                'volume': volume
            })

        df = pd.DataFrame(data)
        df.set_index('timestamp', inplace=True)

        logger.info(f"Generated {len(df)} demo data points for {symbol}")
        return df

    except Exception as e:
        logger.error(f"Error generating demo data: {e}")
        return pd.DataFrame()


async def collect_data(symbol: str, hours: int = 24):
    """Collect historical market data and recent tweets."""
    logger.info(f"Starting data collection for {symbol}")

    # Initialize clients
    hyperliquid = HyperliquidClient()
    twitter = TwitterClient()
    feature_pipeline = FeaturePipeline()

    # Collect market data
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(hours=hours)

    logger.info(f"Collecting market data from {start_time} to {end_time}")
    market_data = hyperliquid.get_historical_data(
        symbol=symbol,
        interval="1m",
        start_time=start_time,
        end_time=end_time
    )

    if market_data.empty:
        logger.warning("No market data from Hyperliquid, generating demo data for testing")
        market_data = generate_demo_market_data(symbol, start_time, end_time)

    if market_data.empty:
        logger.error("No market data available")
        return

    logger.info(f"Collected {len(market_data)} market data points")

    # Collect Twitter data if configured
    tweets = None
    if twitter.is_configured():
        logger.info("Collecting Twitter sentiment data")
        tweets = twitter.get_crypto_tweets([symbol], max_results=1000, hours_back=hours)

        if tweets:
            logger.info(f"Collected {len(tweets)} tweets")
        else:
            logger.warning("No tweets collected")
    else:
        logger.warning("Twitter API not configured, skipping sentiment collection")

    # Create features using the pipeline
    logger.info("Creating features...")
    features_df = feature_pipeline.create_features(
        market_data=market_data,
        tweets=tweets,
        symbol=symbol,
        timeframe="1min"
    )

    if not features_df.empty:
        # Save features
        feature_pipeline.save_features(features_df, symbol, "1min")
        logger.info(f"Features created and saved: {len(features_df)} rows, {len(features_df.columns)} columns")

        # Show feature summary
        logger.info("Feature groups:")
        groups = feature_pipeline.get_feature_importance_groups()
        for group_name, feature_list in groups.items():
            available_features = [f for f in feature_list if f in features_df.columns]
            if available_features:
                logger.info(f"  {group_name}: {len(available_features)} features")

        # Generate LLM-powered market analysis if available
        try:
            logger.info("Generating AI-powered market analysis...")
            llm_advisor = LLMTradingAdvisor(use_local_model=False)  # Use rule-based for demo

            # Create sample regime info for analysis
            regime_info = {
                'regime': 'Bull Market',  # This would come from trained model
                'confidence': 0.75,
                'probabilities': {'Bull Market': 0.75, 'Bear Market': 0.15, 'Sideways Market': 0.10}
            }

            # Generate quick market analysis
            current_price = features_df['close'].iloc[-1]
            from crypto_markov_trader.models import TradingSignal

            quick_analysis = llm_advisor.generate_quick_recommendation(
                symbol=symbol,
                current_price=current_price,
                signal=TradingSignal.HOLD,
                regime=regime_info['regime'],
                confidence=regime_info['confidence']
            )

            if 'error' not in quick_analysis:
                logger.info("🤖 AI Market Analysis:")
                logger.info(f"   Recommendation: {quick_analysis.get('recommendation', 'N/A')}")
                logger.info(f"   Analysis: {quick_analysis.get('analysis', 'N/A')}")

        except Exception as e:
            logger.warning(f"LLM analysis not available: {e}")
    else:
        logger.error("No features created")

    logger.info("Data collection and feature engineering completed successfully")


def run_backtest(symbol: str = "BTC", timeframe: str = "1min", model_path: str = None):
    """Run backtesting on historical data."""
    logger.info(f"Starting backtesting for {symbol}")

    try:
        # Load or train model
        if model_path:
            logger.info(f"Loading model from {model_path}")
            model = MarkovRegimeModel()
            model.load_model(model_path)

            if not model.is_fitted:
                logger.error("Failed to load model")
                return
        else:
            logger.info("No model path provided, training new model...")
            trainer = ModelTrainer()
            model, classifier, evaluation = trainer.train_complete_pipeline(symbol, timeframe)

            if model is None:
                logger.error("Model training failed")
                return

        # Load features and price data
        pipeline = FeaturePipeline()
        features_df = pipeline.load_features(symbol, timeframe)

        if features_df is None or features_df.empty:
            logger.error(f"No features found for {symbol}")
            return

        # Extract price data
        price_columns = ['open', 'high', 'low', 'close', 'volume']
        available_price_cols = [col for col in price_columns if col in features_df.columns]

        if not available_price_cols:
            logger.error("No price data found in features")
            return

        price_data = features_df[available_price_cols].copy()

        # Create classifier and generate signals
        classifier = RegimeClassifier(model)
        signals_df = classifier.generate_signals(features_df)

        if signals_df.empty:
            logger.error("No trading signals generated")
            return

        logger.info(f"Generated {len(signals_df)} trading signals")

        # Run backtest
        logger.info("Running backtest...")
        engine = BacktestEngine(
            initial_capital=settings.default_position_size * 100,  # Scale up for demo
            commission_rate=settings.transaction_cost,
            risk_per_trade=settings.risk_percentage
        )

        backtest_results = engine.run_backtest(signals_df, price_data, classifier)

        if not backtest_results:
            logger.error("Backtest failed")
            return

        # Analyze results
        logger.info("Analyzing backtest results...")
        metrics = PerformanceMetrics()
        analysis = metrics.analyze_backtest_results(backtest_results)

        # Display results
        logger.info("Backtest Results:")
        logger.info("=" * 50)

        summary = backtest_results.get('summary', {})
        logger.info(f"Initial Capital: ${summary.get('initial_capital', 0):,.2f}")
        logger.info(f"Final Equity: ${summary.get('final_equity', 0):,.2f}")
        logger.info(f"Total Return: {summary.get('total_return_pct', 0):.2f}%")
        logger.info(f"Max Drawdown: {summary.get('max_drawdown_pct', 0):.2f}%")
        logger.info(f"Total Trades: {summary.get('total_trades', 0)}")
        logger.info(f"Win Rate: {summary.get('win_rate', 0)*100:.1f}%")

        advanced_metrics = backtest_results.get('advanced_metrics', {})
        if advanced_metrics:
            logger.info(f"Sharpe Ratio: {advanced_metrics.get('sharpe_ratio', 0):.2f}")
            logger.info(f"Sortino Ratio: {advanced_metrics.get('sortino_ratio', 0):.2f}")
            logger.info(f"Profit Factor: {advanced_metrics.get('profit_factor', 0):.2f}")

        # Generate and display performance report
        if analysis:
            report = metrics.generate_performance_report(analysis)
            logger.info("\n" + report)

        logger.info("Backtesting completed successfully!")

    except Exception as e:
        logger.error(f"Error in backtesting: {e}")
        import traceback
        traceback.print_exc()


async def run_live_trading(symbols: str = "BTC,ETH", timeframe: str = "1min", model_path: str = None):
    """Run live trading with real-time execution."""
    logger.info(f"Starting live trading for {symbols}")

    try:
        # Parse symbols
        symbol_list = [s.strip() for s in symbols.split(',')]

        # Initialize live trader
        live_trader = LiveTrader(
            symbols=symbol_list,
            initial_capital=settings.default_position_size * 100,
            model_path=model_path
        )

        # Initialize monitoring
        monitor = TradingMonitor(live_trader)

        logger.info("🚀 Live Trading System Initialized")
        logger.info("=" * 50)
        logger.info(f"Symbols: {symbol_list}")
        logger.info(f"Initial Capital: ${live_trader.initial_capital:,.2f}")
        logger.info(f"Risk Management: Enabled")
        logger.info(f"LLM Analysis: {'Enabled' if live_trader.llm_advisor.is_available() else 'Rule-based fallback'}")

        # Display initial status
        status = live_trader.get_status()
        logger.info(f"Portfolio Value: ${status['portfolio_value']:,.2f}")
        logger.info(f"Risk Score: {status['risk_score']:.1f}/100")

        logger.info("\n⚠️  DEMO MODE NOTICE:")
        logger.info("This is a demonstration of the live trading system.")
        logger.info("In production, this would connect to real exchanges.")
        logger.info("Current implementation uses simulated market data and execution.")

        # Ask for confirmation
        logger.info("\nPress Ctrl+C to stop trading at any time.")
        logger.info("Starting live trading in 5 seconds...")

        import asyncio
        await asyncio.sleep(5)

        # Start trading and monitoring
        logger.info("🎯 Starting live trading execution...")

        # Create tasks for trading and monitoring
        trading_task = asyncio.create_task(live_trader.start_trading())
        monitoring_task = asyncio.create_task(monitor.start_monitoring())

        # Run both tasks concurrently
        try:
            await asyncio.gather(trading_task, monitoring_task)
        except KeyboardInterrupt:
            logger.info("\n🛑 Shutdown signal received...")

            # Graceful shutdown
            await live_trader.stop_trading()
            await monitor.stop_monitoring()

            # Generate final report
            logger.info("Generating final trading report...")
            final_status = live_trader.get_status()

            logger.info("\n" + "="*50)
            logger.info("FINAL TRADING REPORT")
            logger.info("="*50)
            logger.info(f"Trading Duration: {final_status.get('trading_stats', {}).get('start_time', 'Unknown')}")
            logger.info(f"Final Portfolio Value: ${final_status['portfolio_value']:,.2f}")
            logger.info(f"Total P&L: ${final_status['daily_pnl']:,.2f}")
            logger.info(f"Total Trades: {final_status.get('trading_stats', {}).get('executed_trades', 0)}")
            logger.info(f"Risk Score: {final_status['risk_score']:.1f}/100")

            # Display positions
            positions = live_trader.portfolio_manager.get_all_positions()
            if positions:
                logger.info(f"\nFinal Positions:")
                for symbol, position in positions.items():
                    logger.info(f"  {symbol}: {position.quantity:.6f} @ ${position.average_price:.2f}")
                    logger.info(f"    Unrealized P&L: ${position.unrealized_pnl:.2f}")
            else:
                logger.info("\nNo open positions")

            logger.info("\nLive trading session completed.")

    except Exception as e:
        logger.error(f"Error in live trading: {e}")
        import traceback
        traceback.print_exc()


def train_model(symbol: str = "BTC", timeframe: str = "1min", n_components: int = 3):
    """Train the HMM model."""
    logger.info(f"Starting model training for {symbol}")

    try:
        # Initialize trainer
        trainer = ModelTrainer()

        # Train complete pipeline
        model, classifier, evaluation = trainer.train_complete_pipeline(
            symbol=symbol,
            timeframe=timeframe,
            n_components=n_components
        )

        if model is None:
            logger.error("Model training failed")
            return

        logger.info("Model training completed successfully!")

        # Display evaluation results
        if evaluation:
            logger.info("Model Evaluation Results:")
            logger.info("-" * 40)

            model_info = evaluation.get('model_info', {})
            logger.info(f"Components: {model_info.get('n_components', 'N/A')}")
            logger.info(f"Features: {model_info.get('n_features', 'N/A')}")
            logger.info(f"Training samples: {model_info.get('training_samples', 'N/A')}")
            logger.info(f"Converged: {model_info.get('converged', 'N/A')}")

            regime_dist = evaluation.get('regime_distribution', {})
            if regime_dist:
                logger.info("\nRegime Distribution:")
                for regime, count in regime_dist.items():
                    logger.info(f"  {regime}: {count} periods")

            signal_perf = evaluation.get('signal_performance', {})
            if signal_perf:
                logger.info("\nSignal Performance:")
                for signal, metrics in signal_perf.items():
                    logger.info(f"  {signal}: {metrics.get('count', 0)} trades, "
                              f"{metrics.get('win_rate', 0):.2%} win rate")

    except Exception as e:
        logger.error(f"Error in model training: {e}")
        import traceback
        traceback.print_exc()


async def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Crypto Markov Trader")
    parser.add_argument("command", choices=["collect", "train", "backtest", "trade"],
                       help="Command to execute")
    parser.add_argument("--symbol", default="BTC", help="Trading symbol")
    parser.add_argument("--hours", type=float, default=24, help="Hours of data to collect")
    parser.add_argument("--timeframe", default="1min", help="Data timeframe")
    parser.add_argument("--components", type=int, default=3, help="Number of HMM components")
    parser.add_argument("--model-path", help="Path to trained model file")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging()
    
    logger.info(f"Starting Crypto Markov Trader - Command: {args.command}")
    logger.info(f"Configuration: {settings.dict()}")
    
    try:
        if args.command == "collect":
            await collect_data(args.symbol, args.hours)
        elif args.command == "train":
            train_model(args.symbol, args.timeframe, args.components)
        elif args.command == "backtest":
            run_backtest(args.symbol, args.timeframe, args.model_path)
        elif args.command == "trade":
            await run_live_trading(args.symbol, args.timeframe, args.model_path)
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Error: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
