# Crypto Markov Trader

A sophisticated cryptocurrency trading system that combines Hidden Markov Models (HMM) with sentiment analysis for regime-based trading on Hyperliquid.

## 🎯 Project Vision

This project implements an advanced quantitative trading system that goes beyond simple trend-following strategies. It uses Hidden Markov Models to identify unobservable market regimes (high volatility, stable uptrends, bear markets) and combines this with real-time Twitter sentiment analysis to make more informed trading decisions.

## 🏗️ Architecture

The system is built with a modular Python architecture:

- **Data Collection**: Real-time market data from Hyperliquid + Twitter sentiment data
- **Feature Engineering**: Technical indicators (pandas-ta) + sentiment scores (Hugging Face Transformers)
- **Model Core**: Hidden Semi-Markov Models (hsmmlearn) for regime identification
- **Backtesting**: Comprehensive evaluation framework with financial metrics
- **Deployment**: Docker-based microservice architecture

## 🚀 Key Features

- **Regime Detection**: Identifies market states using Hidden Markov Models
- **Sentiment Analysis**: Real-time Twitter sentiment using transformer models
- **High-Frequency Trading**: WebSocket connections for real-time data
- **Risk Management**: Position sizing and risk controls
- **Backtesting**: Historical performance evaluation
- **Modular Design**: Easy to extend and customize

## 📦 Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd crypto-markov-trader
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

## ⚙️ Configuration

### Required API Keys

1. **Twitter/X API**: Get from [Twitter Developer Portal](https://developer.twitter.com/)
2. **Hyperliquid**: No API key required for market data

### Environment Variables

Edit `.env` file with your configuration:

```env
# Twitter API
TWITTER_BEARER_TOKEN=your_token_here
TWITTER_API_KEY=your_key_here
# ... other settings
```

## 🏃‍♂️ Quick Start

### 1. Collect Historical Data
```python
from crypto_markov_trader.data_collection import HyperliquidClient

client = HyperliquidClient()
data = client.get_historical_data("BTC", interval="1m", limit=1000)
```

### 2. Analyze Sentiment
```python
from crypto_markov_trader.data_collection import TwitterClient

twitter = TwitterClient()
tweets = twitter.get_crypto_tweets(["BTC", "ETH"], max_results=100)
```

### 3. Train HMM Model
```python
from crypto_markov_trader.models import MarkovRegimeModel

model = MarkovRegimeModel(n_components=3)
model.fit(features_data)
regimes = model.predict(current_data)
```

## 📊 Project Structure

```
crypto_markov_trader/
├── data_collection/          # Data gathering modules
│   ├── hyperliquid_client.py # Market data from Hyperliquid
│   ├── twitter_client.py     # Twitter sentiment data
│   └── data_aggregator.py    # Data synchronization
├── feature_engineering/      # Feature creation
│   ├── technical_indicators.py
│   └── sentiment_analyzer.py
├── models/                   # ML models
│   ├── hmm_model.py         # Hidden Markov Model
│   └── regime_classifier.py
├── backtesting/             # Strategy evaluation
│   ├── backtest_engine.py
│   └── metrics.py
└── deployment/              # Production deployment
    ├── docker/
    └── services/
```

## 🔄 Development Phases

- [x] **Phase 1**: Project setup and structure
- [ ] **Phase 2**: Data pipeline infrastructure
- [ ] **Phase 3**: Feature engineering pipeline
- [ ] **Phase 4**: HMM model implementation
- [ ] **Phase 5**: Backtesting framework
- [ ] **Phase 6**: LLM integration (optional)
- [ ] **Phase 7**: Docker deployment

## 🧪 Testing

Run tests with pytest:
```bash
pytest tests/ -v
```

## 📈 Performance Metrics

The system evaluates performance using:
- **Sharpe Ratio**: Risk-adjusted returns
- **Maximum Drawdown**: Worst peak-to-trough decline
- **Win Rate**: Percentage of profitable trades
- **Profit Factor**: Gross profit / Gross loss

## 🐳 Docker Deployment

The production system uses a microservice architecture:

1. **hyperliquid_collector**: Market data collection
2. **twitter_collector**: Sentiment data collection
3. **retrainer**: Periodic model retraining
4. **trader**: Live trading execution

```bash
docker-compose up -d
```

## ⚠️ Risk Disclaimer

This software is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. Never trade with money you cannot afford to lose.

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For questions and support, please open an issue on GitHub.
