#!/usr/bin/env python3
"""
Demo script showing HMM model training and regime classification.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

from crypto_markov_trader.models import MarkovRegimeModel, RegimeClassifier, TradingSignal, ModelTrainer
from crypto_markov_trader.feature_engineering import FeaturePipeline
from crypto_markov_trader.utils import setup_logging


def create_sample_market_data():
    """Create sample market data with different regimes."""
    print("Creating sample market data with regime patterns...")
    
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=2000, freq='1min')
    
    # Create data with three distinct regimes
    n_samples = len(dates)
    regime_changes = [0, 600, 1200, 1800, n_samples]  # Regime change points
    
    prices = []
    volumes = []
    
    base_price = 50000
    current_price = base_price
    
    for i in range(n_samples):
        # Determine current regime
        regime = 0
        for j, change_point in enumerate(regime_changes[1:]):
            if i < change_point:
                regime = j
                break
        
        # Different market behaviors for each regime
        if regime == 0:  # Bear market
            trend = -0.0002
            volatility = 0.003
            volume_base = 200
        elif regime == 1:  # Sideways market
            trend = 0.0000
            volatility = 0.001
            volume_base = 150
        elif regime == 2:  # Bull market
            trend = 0.0003
            volatility = 0.002
            volume_base = 300
        else:  # High volatility
            trend = 0.0001
            volatility = 0.005
            volume_base = 400
        
        # Generate price change
        change = trend + np.random.normal(0, volatility)
        current_price *= (1 + change)
        prices.append(current_price)
        
        # Generate volume
        volume = volume_base * (1 + np.random.normal(0, 0.3))
        volumes.append(max(volume, 50))
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': volumes
    })
    
    df.set_index('timestamp', inplace=True)
    print(f"Created {len(df)} data points with regime changes at: {regime_changes[1:-1]}")
    return df


def demo_hmm_training():
    """Demonstrate HMM model training."""
    print("\n" + "="*60)
    print("HMM MODEL TRAINING DEMO")
    print("="*60)
    
    # Create sample data
    market_data = create_sample_market_data()
    
    # Create features using the pipeline
    print("Creating features...")
    pipeline = FeaturePipeline()
    features_df = pipeline.create_features(market_data, symbol="BTC")
    
    print(f"Features created: {len(features_df)} rows, {len(features_df.columns)} columns")
    
    # Select features for HMM
    feature_groups = pipeline.get_feature_importance_groups()
    selected_features = []
    
    # Use trend and momentum features
    for group in ['trend', 'momentum', 'volatility']:
        if group in feature_groups:
            group_features = [f for f in feature_groups[group] if f in features_df.columns]
            selected_features.extend(group_features[:3])  # Take first 3 from each group
    
    print(f"Selected {len(selected_features)} features: {selected_features}")
    
    # Train HMM model
    print("\nTraining HMM model with 3 components...")
    model = MarkovRegimeModel(n_components=3, n_iter=50)
    model.fit(features_df, selected_features)
    
    if model.is_fitted:
        print("✅ Model training completed successfully!")
        print(f"Log-likelihood: {model.training_history['log_likelihood'][-1]:.2f}")
        print(f"Converged: {model.training_history['converged']}")
        print(f"Regime labels: {list(model.regime_labels.values())}")
    else:
        print("❌ Model training failed")
        return None, None
    
    return model, features_df


def demo_regime_prediction(model, features_df):
    """Demonstrate regime prediction and analysis."""
    print("\n" + "="*60)
    print("REGIME PREDICTION DEMO")
    print("="*60)
    
    # Get regime predictions
    print("Predicting market regimes...")
    regime_summary = model.get_regime_summary(features_df)
    
    print(f"Predicted regimes for {len(regime_summary)} time periods")
    
    # Analyze regime distribution
    regime_counts = regime_summary['regime_label'].value_counts()
    print("\nRegime Distribution:")
    for regime, count in regime_counts.items():
        percentage = (count / len(regime_summary)) * 100
        print(f"  {regime}: {count} periods ({percentage:.1f}%)")
    
    # Show regime transitions
    regime_changes = regime_summary[regime_summary['regime_state'] != regime_summary['regime_state'].shift(1)]
    print(f"\nFound {len(regime_changes)} regime transitions:")
    
    for i, (timestamp, row) in enumerate(regime_changes.head(10).iterrows()):
        print(f"  {timestamp}: → {row['regime_label']} (confidence: {row[f'prob_{row[\"regime_label\"].lower().replace(\" \", \"_\")}']:.2f})")
    
    # Show recent predictions
    print("\nRecent regime predictions:")
    recent = regime_summary.tail(10)
    for timestamp, row in recent.iterrows():
        print(f"  {timestamp}: {row['regime_label']} (state: {row['regime_state']})")
    
    return regime_summary


def demo_trading_signals(model, features_df):
    """Demonstrate trading signal generation."""
    print("\n" + "="*60)
    print("TRADING SIGNALS DEMO")
    print("="*60)
    
    # Create regime classifier
    classifier = RegimeClassifier(model)
    
    # Generate trading signals
    print("Generating trading signals...")
    signals_df = classifier.generate_signals(features_df)
    
    print(f"Generated signals for {len(signals_df)} periods")
    
    # Analyze signal distribution
    signal_counts = signals_df['final_signal'].value_counts()
    print("\nSignal Distribution:")
    for signal_value, count in signal_counts.items():
        signal_name = TradingSignal(signal_value).name
        percentage = (count / len(signals_df)) * 100
        print(f"  {signal_name}: {count} periods ({percentage:.1f}%)")
    
    # Show signal changes
    signal_changes = signals_df[signals_df['signal_changed'] == 1]
    print(f"\nFound {len(signal_changes)} signal changes:")
    
    for i, (timestamp, row) in enumerate(signal_changes.head(10).iterrows()):
        signal_name = TradingSignal(row['final_signal']).name
        print(f"  {timestamp}: → {signal_name} ({row['regime_label']}, strength: {row['signal_strength']:.2f})")
    
    # Get current signal
    current_signal = classifier.get_current_signal(features_df)
    print(f"\nCurrent Signal: {current_signal}")
    
    return signals_df, classifier


def demo_performance_analysis(classifier, signals_df, features_df):
    """Demonstrate signal performance analysis."""
    print("\n" + "="*60)
    print("PERFORMANCE ANALYSIS DEMO")
    print("="*60)
    
    # Extract price data
    price_data = features_df[['open', 'high', 'low', 'close', 'volume']]
    
    # Analyze signal performance
    print("Analyzing signal performance...")
    performance = classifier.analyze_signal_performance(signals_df, price_data, forward_periods=10)
    
    if performance:
        print("\nSignal Performance (10-period forward returns):")
        print("-" * 50)
        for signal_name, metrics in performance.items():
            print(f"{signal_name}:")
            print(f"  Trades: {metrics['count']}")
            print(f"  Mean Return: {metrics['mean_return']:.4f} ({metrics['mean_return']*100:.2f}%)")
            print(f"  Win Rate: {metrics['win_rate']:.2%}")
            print(f"  Best Return: {metrics['best_return']:.4f}")
            print(f"  Worst Return: {metrics['worst_return']:.4f}")
            print()
    else:
        print("No performance data available")
    
    # Analyze regime transitions
    transitions = classifier.get_regime_transitions(signals_df)
    if not transitions.empty:
        print(f"Regime Transitions Analysis:")
        print(f"Total transitions: {len(transitions)}")
        
        transition_types = transitions['transition_type'].value_counts()
        for trans_type, count in transition_types.items():
            print(f"  {trans_type}: {count}")
    
    return performance


def main():
    """Run the complete HMM demo."""
    setup_logging()
    
    print("🚀 Crypto Markov Trader - HMM Model Demo")
    print("This demo showcases Hidden Markov Model training and regime classification.")
    
    try:
        # Demo 1: Train HMM model
        model, features_df = demo_hmm_training()
        
        if model is None:
            print("❌ Demo failed - could not train model")
            return
        
        # Demo 2: Predict regimes
        regime_summary = demo_regime_prediction(model, features_df)
        
        # Demo 3: Generate trading signals
        signals_df, classifier = demo_trading_signals(model, features_df)
        
        # Demo 4: Analyze performance
        performance = demo_performance_analysis(classifier, signals_df, features_df)
        
        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY! ✅")
        print("="*60)
        print("Key achievements:")
        print("• Trained HMM model with 3 market regimes")
        print("• Generated regime predictions with confidence scores")
        print("• Created actionable trading signals")
        print("• Analyzed signal performance and transitions")
        print("• System is ready for backtesting and live trading")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
