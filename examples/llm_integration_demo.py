#!/usr/bin/env python3
"""
Demo script showing LLM integration for contextual trading recommendations.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON>ta

from crypto_markov_trader.llm_integration import LLMTradingAdvisor, PromptBuilder, ContextAnalyzer
from crypto_markov_trader.models import MarkovRegimeModel, RegimeClassifier, TradingSignal
from crypto_markov_trader.feature_engineering import FeaturePipeline
from crypto_markov_trader.utils import setup_logging


def create_comprehensive_market_data():
    """Create comprehensive market data for LLM analysis."""
    print("Creating comprehensive market data...")
    
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
    
    # Generate realistic market data with trends
    base_price = 50000
    prices = []
    volumes = []
    
    # Create different market phases
    phases = [
        {'name': 'Accumulation', 'trend': 0.0001, 'volatility': 0.001, 'duration': 300},
        {'name': 'Bull Run', 'trend': 0.0005, 'volatility': 0.002, 'duration': 400},
        {'name': 'Distribution', 'trend': -0.0001, 'volatility': 0.003, 'duration': 200},
        {'name': 'Bear Market', 'trend': -0.0003, 'volatility': 0.004, 'duration': 100}
    ]
    
    current_price = base_price
    phase_idx = 0
    phase_counter = 0
    current_phase = phases[phase_idx]
    
    for i in range(len(dates)):
        # Switch phase if needed
        if phase_counter >= current_phase['duration']:
            phase_idx = (phase_idx + 1) % len(phases)
            current_phase = phases[phase_idx]
            phase_counter = 0
            print(f"Switching to {current_phase['name']} phase at period {i}")
        
        # Generate price movement
        trend = current_phase['trend']
        volatility = current_phase['volatility']
        
        change = trend + np.random.normal(0, volatility)
        current_price *= (1 + change)
        prices.append(current_price)
        
        # Generate volume with phase-specific characteristics
        base_volume = 500
        if current_phase['name'] == 'Bull Run':
            volume_mult = 1.5
        elif current_phase['name'] == 'Bear Market':
            volume_mult = 2.0
        else:
            volume_mult = 1.0
        
        volume = base_volume * volume_mult * (1 + np.random.normal(0, 0.3))
        volumes.append(max(volume, 50))
        
        phase_counter += 1
    
    # Create comprehensive DataFrame
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': volumes
    })
    
    df.set_index('timestamp', inplace=True)
    print(f"Created {len(df)} data points with realistic market phases")
    return df


def demo_context_analysis():
    """Demonstrate context analysis capabilities."""
    print("\n" + "="*60)
    print("CONTEXT ANALYSIS DEMO")
    print("="*60)
    
    # Create market data and features
    market_data = create_comprehensive_market_data()
    
    print("Creating features...")
    pipeline = FeaturePipeline()
    features_df = pipeline.create_features(market_data, symbol="BTC")
    
    # Create sample regime info
    regime_info = {
        'regime': 'Bull Market',
        'confidence': 0.78,
        'probabilities': {
            'Bull Market': 0.78,
            'Bear Market': 0.12,
            'Sideways Market': 0.10
        }
    }
    
    # Create sample sentiment data
    sentiment_data = {
        'compound': 0.35,
        'positive': 0.65,
        'neutral': 0.25,
        'negative': 0.10,
        'trend_1h': 'Rising',
        'trend_4h': 'Stable',
        'volatility': 0.12,
        'tweet_count': 250,
        'engagement_rate': 0.08,
        'top_keywords': ['bitcoin', 'bullish', 'breakout', 'moon', 'hodl']
    }
    
    # Analyze context
    print("Analyzing comprehensive market context...")
    analyzer = ContextAnalyzer()
    context = analyzer.gather_comprehensive_context(
        symbol="BTC",
        features_df=features_df,
        regime_info=regime_info,
        sentiment_data=sentiment_data,
        include_news=False
    )
    
    print("✅ Context analysis completed")
    
    # Display context summary
    print("\n📊 Context Summary:")
    print("-" * 30)
    
    if 'market_summary' in context:
        market_summary = context['market_summary']
        print(f"Current Price: ${market_summary.get('current_price', 0):,.2f}")
        
        price_changes = market_summary.get('price_changes', {})
        for period, change in price_changes.items():
            print(f"{period} Change: {change:+.2%}")
    
    if 'technical_analysis' in context:
        technical = context['technical_analysis']
        bias = technical.get('overall_technical_bias', 'Unknown')
        print(f"Technical Bias: {bias}")
        
        trend_indicators = technical.get('trend_indicators', {})
        print(f"Trend Indicators: {len(trend_indicators)} analyzed")
    
    if 'regime_analysis' in context:
        regime = context['regime_analysis']
        print(f"Current Regime: {regime.get('current_regime', 'Unknown')}")
        print(f"Regime Confidence: {regime.get('regime_confidence', 0):.1%}")
    
    if 'risk_analysis' in context:
        risk = context['risk_analysis']
        volatility = risk.get('volatility', {})
        print(f"Volatility Regime: {volatility.get('regime', 'Unknown')}")
    
    return context, features_df, regime_info, sentiment_data


def demo_prompt_building(context, features_df, regime_info, sentiment_data):
    """Demonstrate prompt building capabilities."""
    print("\n" + "="*60)
    print("PROMPT BUILDING DEMO")
    print("="*60)
    
    builder = PromptBuilder()
    
    # Build comprehensive prompt
    print("Building comprehensive trading analysis prompt...")
    
    current_data = features_df.iloc[-1]
    technical_indicators = {
        'ema_12': current_data.get('ema_12', 50000),
        'ema_26': current_data.get('ema_26', 50000),
        'rsi': current_data.get('rsi', 50),
        'macd': current_data.get('macd', 0),
        'macd_signal': current_data.get('macd_signal', 0),
        'bb_upper': current_data.get('bb_upper', 52000),
        'bb_lower': current_data.get('bb_lower', 48000),
        'atr': current_data.get('atr', 1000),
        'volume_ratio': current_data.get('volume_ratio', 1.0)
    }
    
    comprehensive_prompt = builder.build_comprehensive_prompt(
        symbol="BTC",
        current_data=current_data,
        regime_info=regime_info,
        technical_indicators=technical_indicators,
        sentiment_data=sentiment_data
    )
    
    print("✅ Comprehensive prompt built")
    print(f"Prompt length: {len(comprehensive_prompt)} characters")
    
    # Show prompt sections
    print("\n📝 Prompt Structure:")
    print("-" * 30)
    sections = comprehensive_prompt.split('\n\n')
    for i, section in enumerate(sections[:5]):  # Show first 5 sections
        if section.strip():
            header = section.split('\n')[0]
            print(f"{i+1}. {header}")
    
    # Build quick analysis prompt
    print("\nBuilding quick analysis prompt...")
    quick_prompt = builder.build_quick_analysis_prompt(
        symbol="BTC",
        current_price=current_data['close'],
        signal=TradingSignal.BUY,
        regime="Bull Market",
        confidence=0.78
    )
    
    print("✅ Quick prompt built")
    print(f"Quick prompt length: {len(quick_prompt)} characters")
    
    return comprehensive_prompt, quick_prompt


def demo_llm_advisor(context, features_df, regime_info, sentiment_data):
    """Demonstrate LLM trading advisor capabilities."""
    print("\n" + "="*60)
    print("LLM TRADING ADVISOR DEMO")
    print("="*60)
    
    # Initialize advisor (without local model for demo)
    advisor = LLMTradingAdvisor(use_local_model=False)
    
    print(f"LLM Advisor initialized (Available: {advisor.is_available()})")
    
    # Generate comprehensive recommendation
    print("Generating comprehensive trading recommendation...")
    
    recommendation = advisor.generate_trading_recommendation(
        symbol="BTC",
        features_df=features_df,
        regime_info=regime_info,
        sentiment_data=sentiment_data,
        include_detailed_analysis=False  # Use rule-based for demo
    )
    
    if 'error' in recommendation:
        print(f"❌ Error: {recommendation['error']}")
        return
    
    print("✅ Trading recommendation generated")
    
    # Display recommendation
    print("\n🎯 Trading Recommendation:")
    print("=" * 40)
    
    rec = recommendation.get('recommendation', {})
    print(f"Action: {rec.get('action', 'Unknown')}")
    print(f"Confidence: {rec.get('confidence', 0):.1%}")
    print(f"Position Size: {rec.get('position_size', 0):.1%}")
    print(f"Risk Level: {rec.get('risk_level', 'Unknown')}")
    
    # Display reasoning
    if 'reasoning' in recommendation:
        reasoning = recommendation['reasoning']
        print(f"\n🧠 Analysis Reasoning:")
        print("-" * 30)
        print(f"Technical Score: {reasoning.get('technical_score', 0):+.2f}")
        print(f"Regime Score: {reasoning.get('regime_score', 0):+.2f}")
        print(f"Sentiment Score: {reasoning.get('sentiment_score', 0):+.2f}")
        print(f"Combined Score: {reasoning.get('combined_score', 0):+.2f}")
        
        key_factors = reasoning.get('key_factors', [])
        if key_factors:
            print(f"\nKey Factors:")
            for factor in key_factors:
                print(f"  • {factor}")
    
    # Display price targets
    if 'price_targets' in recommendation:
        targets = recommendation['price_targets']
        print(f"\n🎯 Price Targets:")
        print("-" * 30)
        print(f"Entry: ${targets.get('entry', 0):,.2f}")
        print(f"Stop Loss: ${targets.get('stop_loss', 0):,.2f}")
        print(f"Take Profit: ${targets.get('take_profit', 0):,.2f}")
    
    # Display risk assessment
    if 'risk_assessment' in recommendation:
        risk = recommendation['risk_assessment']
        print(f"\n⚠️  Risk Assessment:")
        print("-" * 30)
        print(f"Volatility Risk: {risk.get('volatility_risk', 'Unknown')}")
        print(f"Drawdown Risk: {risk.get('drawdown_risk', 'Unknown')}")
        
        risk_factors = risk.get('key_risk_factors', [])
        if risk_factors:
            print("Key Risk Factors:")
            for factor in risk_factors:
                print(f"  • {factor}")
    
    # Display monitoring plan
    if 'monitoring_plan' in recommendation:
        monitoring = recommendation['monitoring_plan']
        print(f"\n📊 Monitoring Plan:")
        print("-" * 30)
        
        key_levels = monitoring.get('key_levels_to_watch', [])
        if key_levels:
            print("Key Levels to Watch:")
            for level in key_levels:
                print(f"  • ${level:,.2f}")
        
        indicators = monitoring.get('indicators_to_monitor', [])
        if indicators:
            print(f"Indicators to Monitor: {', '.join(indicators)}")
        
        print(f"Review Frequency: {monitoring.get('review_frequency', 'Unknown')}")
    
    # Generate quick recommendation
    print("\nGenerating quick recommendation...")
    quick_rec = advisor.generate_quick_recommendation(
        symbol="BTC",
        current_price=features_df.iloc[-1]['close'],
        signal=TradingSignal.BUY,
        regime="Bull Market",
        confidence=0.78
    )
    
    print("✅ Quick recommendation generated")
    print(f"Quick Analysis: {quick_rec.get('analysis', 'No analysis')}")
    
    return recommendation


def demo_integration_workflow():
    """Demonstrate complete integration workflow."""
    print("\n" + "="*60)
    print("COMPLETE INTEGRATION WORKFLOW DEMO")
    print("="*60)
    
    print("This demonstrates how LLM integration enhances the trading system:")
    
    # Step 1: Traditional HMM analysis
    print("\n1️⃣  Traditional HMM Analysis:")
    print("   • Market regime identification")
    print("   • Technical indicator signals")
    print("   • Basic trading recommendations")
    
    # Step 2: Enhanced context analysis
    print("\n2️⃣  Enhanced Context Analysis:")
    print("   • Comprehensive market structure analysis")
    print("   • Multi-timeframe technical analysis")
    print("   • Sentiment and social media integration")
    print("   • Risk and volatility assessment")
    
    # Step 3: LLM-powered insights
    print("\n3️⃣  LLM-Powered Insights:")
    print("   • Natural language market analysis")
    print("   • Contextual trading recommendations")
    print("   • Risk-adjusted position sizing")
    print("   • Detailed monitoring plans")
    
    # Step 4: Actionable outputs
    print("\n4️⃣  Actionable Outputs:")
    print("   • Clear BUY/SELL/HOLD recommendations")
    print("   • Specific entry/exit price levels")
    print("   • Risk management guidelines")
    print("   • Performance monitoring framework")
    
    print("\n🚀 Benefits of LLM Integration:")
    print("   ✅ More nuanced market analysis")
    print("   ✅ Better risk assessment")
    print("   ✅ Contextual decision making")
    print("   ✅ Explainable recommendations")
    print("   ✅ Adaptive to market conditions")


def main():
    """Run the complete LLM integration demo."""
    setup_logging()
    
    print("🤖 Crypto Markov Trader - LLM Integration Demo")
    print("This demo showcases AI-powered contextual trading recommendations.")
    
    try:
        # Demo 1: Context analysis
        context, features_df, regime_info, sentiment_data = demo_context_analysis()
        
        # Demo 2: Prompt building
        comprehensive_prompt, quick_prompt = demo_prompt_building(
            context, features_df, regime_info, sentiment_data
        )
        
        # Demo 3: LLM advisor
        recommendation = demo_llm_advisor(
            context, features_df, regime_info, sentiment_data
        )
        
        # Demo 4: Integration workflow
        demo_integration_workflow()
        
        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY! ✅")
        print("="*60)
        print("Key achievements:")
        print("• Comprehensive market context analysis")
        print("• Dynamic prompt generation for LLM queries")
        print("• Rule-based fallback recommendations")
        print("• Detailed risk assessment and monitoring")
        print("• Integration with existing HMM framework")
        print("\nThe LLM integration enhances the trading system with:")
        print("• Contextual market understanding")
        print("• Natural language explanations")
        print("• Adaptive risk management")
        print("• Comprehensive monitoring plans")
        
        print("\n⚠️  Note: This demo uses rule-based analysis.")
        print("For full LLM capabilities, install transformers and torch:")
        print("pip install transformers torch")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
