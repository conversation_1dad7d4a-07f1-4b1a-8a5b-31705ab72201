#!/usr/bin/env python3
"""
Demo script showing comprehensive backtesting capabilities.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_markov_trader.backtesting import BacktestEngine, PerformanceMetrics, StrategyOptimizer
from crypto_markov_trader.models import MarkovRegimeModel, RegimeClassifier, TradingSignal
from crypto_markov_trader.feature_engineering import FeaturePipeline
from crypto_markov_trader.utils import setup_logging


def create_realistic_market_data():
    """Create realistic market data with different market conditions."""
    print("Creating realistic market data...")
    
    np.random.seed(42)
    dates = pd.date_range(start='2024-01-01', periods=5000, freq='1min')
    
    # Create market data with regime changes
    prices = []
    volumes = []
    
    base_price = 50000
    current_price = base_price
    
    # Define market regimes with different characteristics
    regimes = [
        {'name': 'Bull Market', 'trend': 0.0003, 'volatility': 0.002, 'volume_mult': 1.2, 'duration': 1500},
        {'name': 'Bear Market', 'trend': -0.0002, 'volatility': 0.003, 'volume_mult': 1.5, 'duration': 1000},
        {'name': 'Sideways', 'trend': 0.0000, 'volatility': 0.001, 'volume_mult': 0.8, 'duration': 1500},
        {'name': 'High Vol', 'trend': 0.0001, 'volatility': 0.005, 'volume_mult': 2.0, 'duration': 1000}
    ]
    
    regime_idx = 0
    regime_counter = 0
    current_regime = regimes[regime_idx]
    
    for i in range(len(dates)):
        # Switch regime if duration exceeded
        if regime_counter >= current_regime['duration']:
            regime_idx = (regime_idx + 1) % len(regimes)
            current_regime = regimes[regime_idx]
            regime_counter = 0
            print(f"Switching to {current_regime['name']} at period {i}")
        
        # Generate price change based on current regime
        trend = current_regime['trend']
        volatility = current_regime['volatility']
        
        change = trend + np.random.normal(0, volatility)
        current_price *= (1 + change)
        prices.append(current_price)
        
        # Generate volume
        base_volume = 500
        volume = base_volume * current_regime['volume_mult'] * (1 + np.random.normal(0, 0.3))
        volumes.append(max(volume, 50))
        
        regime_counter += 1
    
    # Create OHLCV DataFrame
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': volumes
    })
    
    df.set_index('timestamp', inplace=True)
    print(f"Created {len(df)} data points with realistic market regimes")
    return df


def demo_basic_backtesting():
    """Demonstrate basic backtesting functionality."""
    print("\n" + "="*60)
    print("BASIC BACKTESTING DEMO")
    print("="*60)
    
    # Create market data
    market_data = create_realistic_market_data()
    
    # Create features
    print("Creating features...")
    pipeline = FeaturePipeline()
    features_df = pipeline.create_features(market_data, symbol="BTC")
    
    # Train a simple HMM model
    print("Training HMM model...")
    model = MarkovRegimeModel(n_components=3, n_iter=50)
    
    # Select key features
    feature_cols = ['ema_12', 'ema_26', 'rsi', 'macd', 'bb_width', 'atr', 'volume_ratio']
    available_features = [col for col in feature_cols if col in features_df.columns]
    
    model.fit(features_df, available_features)
    
    if not model.is_fitted:
        print("❌ Model training failed")
        return None, None, None
    
    print("✅ Model trained successfully")
    
    # Generate trading signals
    print("Generating trading signals...")
    classifier = RegimeClassifier(model)
    signals_df = classifier.generate_signals(features_df)
    
    print(f"Generated {len(signals_df)} trading signals")
    
    # Extract price data
    price_data = features_df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    # Run backtest
    print("Running backtest...")
    engine = BacktestEngine(
        initial_capital=100000.0,
        commission_rate=0.001,
        slippage_rate=0.0005,
        risk_per_trade=0.02
    )
    
    backtest_results = engine.run_backtest(signals_df, price_data, classifier)
    
    if not backtest_results:
        print("❌ Backtest failed")
        return None, None, None
    
    print("✅ Backtest completed successfully")
    
    # Display basic results
    summary = backtest_results['summary']
    print(f"\nBasic Results:")
    print(f"Initial Capital: ${summary['initial_capital']:,.2f}")
    print(f"Final Equity: ${summary['final_equity']:,.2f}")
    print(f"Total Return: {summary['total_return_pct']:.2f}%")
    print(f"Max Drawdown: {summary['max_drawdown_pct']:.2f}%")
    print(f"Total Trades: {summary['total_trades']}")
    print(f"Win Rate: {summary['win_rate']*100:.1f}%")
    
    return backtest_results, signals_df, price_data


def demo_performance_analysis(backtest_results):
    """Demonstrate comprehensive performance analysis."""
    print("\n" + "="*60)
    print("PERFORMANCE ANALYSIS DEMO")
    print("="*60)
    
    if not backtest_results:
        print("No backtest results to analyze")
        return
    
    # Analyze performance
    print("Analyzing performance metrics...")
    metrics = PerformanceMetrics()
    analysis = metrics.analyze_backtest_results(backtest_results)
    
    if not analysis:
        print("❌ Performance analysis failed")
        return
    
    print("✅ Performance analysis completed")
    
    # Display detailed analysis
    if 'performance_summary' in analysis:
        perf_summary = analysis['performance_summary']
        
        print("\n📊 Performance Summary:")
        print("-" * 30)
        
        returns = perf_summary.get('returns', {})
        print(f"Annualized Return: {returns.get('annualized_return', 0)*100:.2f}%")
        print(f"Volatility: {returns.get('volatility_annualized', 0)*100:.2f}%")
        print(f"Sharpe Ratio: {returns.get('sharpe_ratio', 0):.2f}")
        print(f"Sortino Ratio: {returns.get('sortino_ratio', 0):.2f}")
        print(f"Calmar Ratio: {returns.get('calmar_ratio', 0):.2f}")
        
        risk = perf_summary.get('risk', {})
        print(f"Max Drawdown: {risk.get('max_drawdown_pct', 0):.2f}%")
        print(f"Profit Factor: {risk.get('profit_factor', 0):.2f}")
        
        print(f"\n🏆 Overall Rating: {perf_summary.get('rating', 'Unknown')}")
    
    # Trade analysis
    if 'trade_analysis' in analysis and 'error' not in analysis['trade_analysis']:
        trade_analysis = analysis['trade_analysis']
        
        print("\n📈 Trade Analysis:")
        print("-" * 30)
        
        if 'trade_distribution' in trade_analysis:
            dist = trade_analysis['trade_distribution']
            
            if 'by_signal' in dist:
                print("Trades by Signal:")
                for signal, count in dist['by_signal'].items():
                    print(f"  {signal}: {count}")
            
            if 'by_regime' in dist:
                print("Trades by Regime:")
                for regime, count in dist['by_regime'].items():
                    print(f"  {regime}: {count}")
    
    # Risk analysis
    if 'risk_analysis' in analysis and 'error' not in analysis['risk_analysis']:
        risk_analysis = analysis['risk_analysis']
        
        print("\n⚠️  Risk Analysis:")
        print("-" * 30)
        
        if 'value_at_risk' in risk_analysis:
            var = risk_analysis['value_at_risk']
            print(f"VaR (95%): {var.get('var_95', 0)*100:.2f}%")
            print(f"VaR (99%): {var.get('var_99', 0)*100:.2f}%")
        
        if 'drawdown_metrics' in risk_analysis:
            dd = risk_analysis['drawdown_metrics']
            print(f"Average Drawdown: {dd.get('average_drawdown', 0)*100:.2f}%")
    
    # Generate full report
    print("\n" + "="*60)
    print("FULL PERFORMANCE REPORT")
    print("="*60)
    
    report = metrics.generate_performance_report(analysis)
    print(report)
    
    return analysis


def demo_strategy_optimization():
    """Demonstrate strategy parameter optimization."""
    print("\n" + "="*60)
    print("STRATEGY OPTIMIZATION DEMO")
    print("="*60)
    
    print("⚠️  Note: This is a simplified optimization demo.")
    print("Full optimization requires more computational resources and time.")
    
    # Create smaller dataset for optimization demo
    market_data = create_realistic_market_data()
    market_data = market_data.iloc[:1000]  # Use smaller dataset
    
    pipeline = FeaturePipeline()
    features_df = pipeline.create_features(market_data, symbol="BTC")
    price_data = features_df[['open', 'high', 'low', 'close', 'volume']].copy()
    
    # Define parameter ranges to optimize
    parameter_ranges = {
        'n_components': [2, 3],
        'commission_rate': [0.001, 0.002],
        'risk_per_trade': [0.01, 0.02],
        'initial_capital': [50000, 100000]
    }
    
    print(f"Testing parameter combinations: {parameter_ranges}")
    
    # Run optimization (simplified version)
    optimizer = StrategyOptimizer(max_workers=1)  # Single worker for demo
    
    print("Running parameter optimization...")
    print("(This may take a few minutes...)")
    
    # For demo purposes, we'll just show the structure
    # In practice, you'd run the full optimization
    print("✅ Optimization framework ready")
    print("Parameter combinations to test:", 
          len(optimizer._generate_parameter_combinations(parameter_ranges)))
    
    # Show what the optimization would return
    mock_results = {
        'best_parameters': {
            'n_components': 3,
            'commission_rate': 0.001,
            'risk_per_trade': 0.02,
            'initial_capital': 100000
        },
        'best_score': 1.45,  # Sharpe ratio
        'optimization_metric': 'sharpe_ratio',
        'total_combinations_tested': 8
    }
    
    print("\n🎯 Mock Optimization Results:")
    print(f"Best Parameters: {mock_results['best_parameters']}")
    print(f"Best Sharpe Ratio: {mock_results['best_score']:.2f}")
    print(f"Combinations Tested: {mock_results['total_combinations_tested']}")
    
    return mock_results


def main():
    """Run the complete backtesting demo."""
    setup_logging()
    
    print("🚀 Crypto Markov Trader - Backtesting Framework Demo")
    print("This demo showcases comprehensive backtesting and performance analysis.")
    
    try:
        # Demo 1: Basic backtesting
        backtest_results, signals_df, price_data = demo_basic_backtesting()
        
        if backtest_results is None:
            print("❌ Demo failed - could not run backtest")
            return
        
        # Demo 2: Performance analysis
        analysis = demo_performance_analysis(backtest_results)
        
        # Demo 3: Strategy optimization
        optimization_results = demo_strategy_optimization()
        
        print("\n" + "="*60)
        print("DEMO COMPLETED SUCCESSFULLY! ✅")
        print("="*60)
        print("Key achievements:")
        print("• Ran comprehensive backtest with realistic market conditions")
        print("• Calculated advanced performance metrics (Sharpe, Sortino, Calmar)")
        print("• Analyzed trade distribution and regime performance")
        print("• Demonstrated risk analysis (VaR, drawdowns)")
        print("• Showed strategy optimization framework")
        print("• Generated detailed performance reports")
        print("\nThe backtesting framework is ready for:")
        print("• Strategy validation and optimization")
        print("• Risk assessment and management")
        print("• Performance monitoring and reporting")
        print("• Parameter sensitivity analysis")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
