#!/usr/bin/env python3
"""
Demo script showing feature engineering capabilities.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from crypto_markov_trader.feature_engineering import FeaturePipeline, TechnicalIndicators, SentimentAnalyzer
from crypto_markov_trader.utils import setup_logging


def create_sample_data():
    """Create sample OHLCV data for demonstration."""
    print("Creating sample market data...")
    
    # Generate 1000 minutes of sample data
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
    np.random.seed(42)
    
    # Generate realistic price data with trend and volatility
    base_price = 50000
    trend = 0.0001  # Slight upward trend
    volatility = 0.002
    
    prices = [base_price]
    for i in range(1, 1000):
        # Add trend and random walk
        change = trend + np.random.normal(0, volatility)
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # Create OHLCV data
    df = pd.DataFrame({
        'timestamp': dates,
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(100, 1000, 1000)
    })
    
    df.set_index('timestamp', inplace=True)
    print(f"Created {len(df)} data points from {df.index[0]} to {df.index[-1]}")
    return df





def demo_technical_indicators():
    """Demonstrate technical indicators calculation."""
    print("\n" + "="*50)
    print("TECHNICAL INDICATORS DEMO")
    print("="*50)
    
    # Create sample data
    market_data = create_sample_data()
    
    # Initialize technical indicators
    ti = TechnicalIndicators()
    
    # Calculate indicators
    print("Calculating technical indicators...")
    features_df = ti.calculate_all_indicators(market_data)
    
    print(f"Original columns: {len(market_data.columns)}")
    print(f"With indicators: {len(features_df.columns)}")
    print(f"Added {len(features_df.columns) - len(market_data.columns)} technical indicators")
    
    # Show some key indicators
    print("\nSample of calculated indicators:")
    indicator_cols = ['close', 'ema_12', 'ema_26', 'rsi', 'macd', 'bb_upper', 'bb_lower', 'atr']
    available_cols = [col for col in indicator_cols if col in features_df.columns]
    print(features_df[available_cols].tail(10))
    
    return features_df





def main():
    """Run technical indicators demo."""
    setup_logging()

    print("🚀 Crypto Markov Trader - Feature Engineering Demo")
    print("This demo showcases the technical indicators calculation.")

    try:
        # Demo: Technical Indicators
        technical_features = demo_technical_indicators()

        print("\n" + "="*50)
        print("DEMO COMPLETED SUCCESSFULLY! ✅")
        print("="*50)
        print("Key takeaways:")
        print("• Technical indicators are calculated using pandas-ta")
        print("• Features include trend, momentum, volatility, and volume indicators")
        print("• The system handles missing data and edge cases gracefully")
        print("• Features are ready for machine learning models")

    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
