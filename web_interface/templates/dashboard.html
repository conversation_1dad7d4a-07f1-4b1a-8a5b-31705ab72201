<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Crypto Markov Trader - Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-active { background-color: #10b981; }
        .status-inactive { background-color: #ef4444; }
        .status-warning { background-color: #f59e0b; }
        
        .metric-card {
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }
        
        .alert-critical { border-left: 4px solid #ef4444; }
        .alert-warning { border-left: 4px solid #f59e0b; }
        .alert-info { border-left: 4px solid #3b82f6; }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-robot text-2xl text-blue-600 mr-3"></i>
                    <h1 class="text-2xl font-bold text-gray-900">Crypto Markov Trader</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div id="system-status" class="flex items-center">
                        <span class="status-indicator status-inactive"></span>
                        <span class="text-sm text-gray-600">Initializing...</span>
                    </div>
                    <button id="emergency-stop" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                        <i class="fas fa-stop mr-2"></i>Emergency Stop
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Control Panel -->
        <div class="bg-white rounded-lg shadow mb-8 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Trading Control</h2>
            <div class="flex space-x-4">
                <button id="start-trading" class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-md font-medium">
                    <i class="fas fa-play mr-2"></i>Start Trading
                </button>
                <button id="stop-trading" class="bg-yellow-600 hover:bg-yellow-700 text-white px-6 py-2 rounded-md font-medium">
                    <i class="fas fa-pause mr-2"></i>Stop Trading
                </button>
                <button id="refresh-data" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md font-medium">
                    <i class="fas fa-sync mr-2"></i>Refresh
                </button>
            </div>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-wallet text-2xl text-green-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Portfolio Value</p>
                        <p id="portfolio-value" class="text-2xl font-semibold text-gray-900">$0.00</p>
                        <p id="portfolio-change" class="text-sm text-gray-500">+0.00%</p>
                    </div>
                </div>
            </div>

            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-chart-line text-2xl text-blue-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Daily P&L</p>
                        <p id="daily-pnl" class="text-2xl font-semibold text-gray-900">$0.00</p>
                        <p id="daily-pnl-pct" class="text-sm text-gray-500">+0.00%</p>
                    </div>
                </div>
            </div>

            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-shield-alt text-2xl text-yellow-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Risk Score</p>
                        <p id="risk-score" class="text-2xl font-semibold text-gray-900">0/100</p>
                        <div id="risk-bar" class="w-full bg-gray-200 rounded-full h-2 mt-2">
                            <div class="bg-green-600 h-2 rounded-full" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-coins text-2xl text-purple-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Open Positions</p>
                        <p id="num-positions" class="text-2xl font-semibold text-gray-900">0</p>
                        <p id="positions-value" class="text-sm text-gray-500">$0.00</p>
                    </div>
                </div>
            </div>

            <div class="metric-card bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <i class="fas fa-brain text-2xl text-indigo-600"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">AI Sentiment</p>
                        <p id="overall-sentiment" class="text-2xl font-semibold text-gray-900">Neutral</p>
                        <p id="sentiment-score" class="text-sm text-gray-500">0.00</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Market Overview -->
        <div class="bg-white rounded-lg shadow mb-8 p-6">
            <h2 class="text-lg font-semibold text-gray-900 mb-4">Market Overview</h2>
            <div id="market-overview" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Market data will be populated here -->
            </div>
        </div>

        <!-- Charts and Analysis Row -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <!-- Portfolio Performance Chart -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Portfolio Performance</h3>
                <canvas id="performance-chart" width="400" height="200"></canvas>
            </div>

            <!-- Technical Indicators -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Technical Indicators</h3>
                <div id="indicators-container">
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">RSI</span>
                            <span id="rsi-value" class="text-sm font-semibold">50.0</span>
                        </div>
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div id="rsi-bar" class="bg-blue-600 h-2 rounded-full" style="width: 50%"></div>
                        </div>

                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">MACD</span>
                            <span id="macd-value" class="text-sm font-semibold">0.0</span>
                        </div>
                        <div id="macd-signal" class="text-xs text-gray-500">Neutral</div>

                        <div class="flex justify-between items-center">
                            <span class="text-sm font-medium text-gray-600">Bollinger Bands</span>
                            <span id="bb-position" class="text-sm font-semibold">Middle</span>
                        </div>

                        <div class="mt-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Price vs Moving Averages</h4>
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-xs text-gray-600">Above SMA20</span>
                                    <span id="sma20-signal" class="text-xs">-</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-xs text-gray-600">Above SMA50</span>
                                    <span id="sma50-signal" class="text-xs">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Twitter Sentiment -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Twitter Sentiment</h3>
                <div id="sentiment-container">
                    <div class="space-y-4">
                        <div class="text-center">
                            <div id="sentiment-gauge" class="mx-auto w-24 h-24 rounded-full border-8 border-gray-200 flex items-center justify-center">
                                <span id="sentiment-emoji" class="text-2xl">😐</span>
                            </div>
                            <p id="sentiment-label" class="mt-2 text-sm font-medium text-gray-600">Neutral</p>
                        </div>

                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-xs text-gray-600">Positive</span>
                                <span id="positive-pct" class="text-xs font-semibold text-green-600">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1">
                                <div id="positive-bar" class="bg-green-600 h-1 rounded-full" style="width: 0%"></div>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-xs text-gray-600">Negative</span>
                                <span id="negative-pct" class="text-xs font-semibold text-red-600">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1">
                                <div id="negative-bar" class="bg-red-600 h-1 rounded-full" style="width: 0%"></div>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-xs text-gray-600">Neutral</span>
                                <span id="neutral-pct" class="text-xs font-semibold text-gray-600">100%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-1">
                                <div id="neutral-bar" class="bg-gray-600 h-1 rounded-full" style="width: 100%"></div>
                            </div>
                        </div>

                        <div class="text-center pt-2 border-t">
                            <p class="text-xs text-gray-500">
                                <span id="tweet-count">0</span> tweets analyzed
                            </p>
                            <p id="sentiment-updated" class="text-xs text-gray-400">Never updated</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Positions and Orders Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Current Positions -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Current Positions</h3>
                <div id="positions-table" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Side</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P&L</th>
                            </tr>
                        </thead>
                        <tbody id="positions-tbody" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">No positions</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Recent Orders -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Orders</h3>
                <div id="orders-table" class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Side</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            </tr>
                        </thead>
                        <tbody id="orders-tbody" class="bg-white divide-y divide-gray-200">
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-gray-500">No recent orders</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Risk Alerts and Logs Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Risk Alerts -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Risk Alerts</h3>
                <div id="risk-alerts" class="space-y-3 max-h-64 overflow-y-auto">
                    <div class="text-center text-gray-500 py-4">No alerts</div>
                </div>
            </div>

            <!-- Recent Logs -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
                <div id="recent-logs" class="space-y-2 max-h-64 overflow-y-auto text-sm">
                    <div class="text-center text-gray-500 py-4">No recent activity</div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Global variables
        let performanceChart = null;
        let updateInterval = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeChart();
            updateDashboard();
            
            // Set up auto-refresh
            updateInterval = setInterval(updateDashboard, 5000); // Update every 5 seconds
            
            // Set up event listeners
            setupEventListeners();
        });

        function initializeChart() {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Portfolio Value',
                        data: [],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }

        function setupEventListeners() {
            document.getElementById('start-trading').addEventListener('click', startTrading);
            document.getElementById('stop-trading').addEventListener('click', stopTrading);
            document.getElementById('emergency-stop').addEventListener('click', emergencyStop);
            document.getElementById('refresh-data').addEventListener('click', updateDashboard);
        }

        async function updateDashboard() {
            try {
                // Update system status
                const statusResponse = await fetch('/api/status');
                const statusData = await statusResponse.json();
                
                updateSystemStatus(statusData);
                updateMetrics(statusData);
                
                // Update positions
                const positionsResponse = await fetch('/api/positions');
                const positionsData = await positionsResponse.json();
                updatePositions(positionsData);
                
                // Update performance chart
                const performanceResponse = await fetch('/api/performance');
                const performanceData = await performanceResponse.json();
                updatePerformanceChart(performanceData);
                
                // Update risk alerts
                const riskResponse = await fetch('/api/risk');
                const riskData = await riskResponse.json();
                updateRiskAlerts(riskData);

                // Update sentiment
                const sentimentResponse = await fetch('/api/sentiment');
                const sentimentData = await sentimentResponse.json();
                updateSentiment(sentimentData);

                // Update technical indicators
                if (statusData.system_status.symbols && statusData.system_status.symbols.length > 0) {
                    const primarySymbol = statusData.system_status.symbols[0];
                    const indicatorsResponse = await fetch(`/api/indicators/${primarySymbol}`);
                    const indicatorsData = await indicatorsResponse.json();
                    updateTechnicalIndicators(indicatorsData);
                }

                // Update logs
                const logsResponse = await fetch('/api/logs');
                const logsData = await logsResponse.json();
                updateLogs(logsData);
                
            } catch (error) {
                console.error('Error updating dashboard:', error);
            }
        }

        function updateSystemStatus(data) {
            const statusElement = document.getElementById('system-status');
            const indicator = statusElement.querySelector('.status-indicator');
            const text = statusElement.querySelector('span:last-child');
            
            if (data.system_status.is_running && data.system_status.trading_enabled) {
                indicator.className = 'status-indicator status-active';
                text.textContent = 'Trading Active';
            } else if (data.system_status.emergency_stop) {
                indicator.className = 'status-indicator status-inactive';
                text.textContent = 'Emergency Stop';
            } else {
                indicator.className = 'status-indicator status-warning';
                text.textContent = 'Trading Paused';
            }
        }

        function updateMetrics(data) {
            // Portfolio Value
            document.getElementById('portfolio-value').textContent = '$' + data.portfolio.total_value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});

            const totalReturn = data.portfolio.total_return || 0;
            const portfolioChangeElement = document.getElementById('portfolio-change');
            portfolioChangeElement.textContent = (totalReturn >= 0 ? '+' : '') + (totalReturn * 100).toFixed(2) + '%';
            portfolioChangeElement.className = totalReturn >= 0 ? 'text-sm text-green-600' : 'text-sm text-red-600';

            // Daily P&L
            const dailyPnl = data.portfolio.daily_pnl;
            const dailyPnlElement = document.getElementById('daily-pnl');
            dailyPnlElement.textContent = (dailyPnl >= 0 ? '+' : '') + '$' + dailyPnl.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});
            dailyPnlElement.className = dailyPnl >= 0 ? 'text-2xl font-semibold text-green-600' : 'text-2xl font-semibold text-red-600';

            const dailyPnlPct = data.portfolio.total_value > 0 ? (dailyPnl / data.portfolio.total_value) * 100 : 0;
            const dailyPnlPctElement = document.getElementById('daily-pnl-pct');
            dailyPnlPctElement.textContent = (dailyPnlPct >= 0 ? '+' : '') + dailyPnlPct.toFixed(2) + '%';
            dailyPnlPctElement.className = dailyPnlPct >= 0 ? 'text-sm text-green-600' : 'text-sm text-red-600';

            // Risk Score
            const riskScore = Math.round(data.risk.risk_score);
            document.getElementById('risk-score').textContent = riskScore + '/100';

            const riskBar = document.querySelector('#risk-bar .bg-green-600, #risk-bar .bg-yellow-600, #risk-bar .bg-red-600');
            if (riskBar) {
                riskBar.style.width = riskScore + '%';
                if (riskScore < 30) {
                    riskBar.className = 'bg-green-600 h-2 rounded-full';
                } else if (riskScore < 70) {
                    riskBar.className = 'bg-yellow-600 h-2 rounded-full';
                } else {
                    riskBar.className = 'bg-red-600 h-2 rounded-full';
                }
            }

            // Positions
            document.getElementById('num-positions').textContent = data.portfolio.num_positions;
            document.getElementById('positions-value').textContent = '$' + data.portfolio.positions_value.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2});

            // Market Data
            if (data.market_data) {
                updateMarketOverview(data.market_data);
            }
        }

        function updateMarketOverview(marketData) {
            const container = document.getElementById('market-overview');
            container.innerHTML = '';

            Object.entries(marketData).forEach(([symbol, data]) => {
                const card = document.createElement('div');
                card.className = 'bg-gray-50 rounded-lg p-4';
                card.innerHTML = `
                    <div class="flex justify-between items-center mb-2">
                        <h4 class="font-semibold text-gray-900">${symbol}</h4>
                        <span class="text-xs text-gray-500">${new Date(data.timestamp).toLocaleTimeString()}</span>
                    </div>
                    <div class="text-2xl font-bold text-gray-900">$${data.price.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</div>
                    <div class="text-sm ${data.change_24h >= 0 ? 'text-green-600' : 'text-red-600'}">
                        ${data.change_24h >= 0 ? '+' : ''}${data.change_24h.toFixed(2)}%
                    </div>
                    <div class="text-xs text-gray-500 mt-1">
                        Vol: ${data.volume.toLocaleString()}
                    </div>
                `;
                container.appendChild(card);
            });
        }

        function updatePositions(positions) {
            const tbody = document.getElementById('positions-tbody');
            
            if (positions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="4" class="px-6 py-4 text-center text-gray-500">No positions</td></tr>';
                return;
            }
            
            tbody.innerHTML = positions.map(position => `
                <tr>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${position.symbol}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${position.side === 'LONG' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                            ${position.side}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${Math.abs(position.quantity).toFixed(6)}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm ${position.unrealized_pnl >= 0 ? 'text-green-600' : 'text-red-600'}">
                        ${(position.unrealized_pnl >= 0 ? '+' : '')}$${position.unrealized_pnl.toFixed(2)}
                    </td>
                </tr>
            `).join('');
        }

        function updatePerformanceChart(data) {
            if (data.portfolio_history && data.portfolio_history.length > 0) {
                const labels = data.portfolio_history.map(item => new Date(item.timestamp).toLocaleTimeString());
                const values = data.portfolio_history.map(item => item.total_value);
                
                performanceChart.data.labels = labels;
                performanceChart.data.datasets[0].data = values;
                performanceChart.update();
            }
        }

        function updateRiskAlerts(data) {
            const alertsContainer = document.getElementById('risk-alerts');
            
            if (!data.recent_alerts || data.recent_alerts.length === 0) {
                alertsContainer.innerHTML = '<div class="text-center text-gray-500 py-4">No alerts</div>';
                return;
            }
            
            alertsContainer.innerHTML = data.recent_alerts.map(alert => `
                <div class="p-3 rounded-md alert-${alert.level.toLowerCase()}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-${alert.level === 'critical' ? 'exclamation-triangle' : alert.level === 'warning' ? 'exclamation-circle' : 'info-circle'} text-${alert.level === 'critical' ? 'red' : alert.level === 'warning' ? 'yellow' : 'blue'}-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-gray-800">${alert.category}</p>
                            <p class="text-sm text-gray-600">${alert.message}</p>
                            <p class="text-xs text-gray-500">${new Date(alert.timestamp).toLocaleString()}</p>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        function updateLogs(logs) {
            const logsContainer = document.getElementById('recent-logs');

            if (!logs || logs.length === 0) {
                logsContainer.innerHTML = '<div class="text-center text-gray-500 py-4">No recent activity</div>';
                return;
            }

            logsContainer.innerHTML = logs.slice(-20).map(log => `
                <div class="text-xs text-gray-600 py-1 border-b border-gray-100">
                    ${log.message}
                </div>
            `).join('');
        }

        function updateSentiment(sentimentData) {
            // Calculate overall sentiment from all symbols
            let totalTweets = 0;
            let weightedSentiment = 0;
            let totalPositive = 0;
            let totalNegative = 0;
            let totalNeutral = 0;

            Object.values(sentimentData).forEach(data => {
                if (data.tweet_count > 0) {
                    totalTweets += data.tweet_count;
                    weightedSentiment += data.sentiment_score * data.tweet_count;
                    totalPositive += data.positive_ratio * data.tweet_count;
                    totalNegative += data.negative_ratio * data.tweet_count;
                    totalNeutral += data.neutral_ratio * data.tweet_count;
                }
            });

            if (totalTweets > 0) {
                const avgSentiment = weightedSentiment / totalTweets;
                const positivePct = (totalPositive / totalTweets) * 100;
                const negativePct = (totalNegative / totalTweets) * 100;
                const neutralPct = (totalNeutral / totalTweets) * 100;

                // Update overall sentiment display
                const sentimentLabel = avgSentiment > 0.1 ? 'Bullish' : avgSentiment < -0.1 ? 'Bearish' : 'Neutral';
                const sentimentEmoji = avgSentiment > 0.1 ? '😊' : avgSentiment < -0.1 ? '😟' : '😐';
                const sentimentColor = avgSentiment > 0.1 ? 'border-green-500' : avgSentiment < -0.1 ? 'border-red-500' : 'border-gray-300';

                document.getElementById('overall-sentiment').textContent = sentimentLabel;
                document.getElementById('sentiment-score').textContent = avgSentiment.toFixed(2);
                document.getElementById('sentiment-emoji').textContent = sentimentEmoji;
                document.getElementById('sentiment-label').textContent = sentimentLabel;
                document.getElementById('sentiment-gauge').className = `mx-auto w-24 h-24 rounded-full border-8 ${sentimentColor} flex items-center justify-center`;

                // Update sentiment bars
                document.getElementById('positive-pct').textContent = positivePct.toFixed(1) + '%';
                document.getElementById('positive-bar').style.width = positivePct + '%';

                document.getElementById('negative-pct').textContent = negativePct.toFixed(1) + '%';
                document.getElementById('negative-bar').style.width = negativePct + '%';

                document.getElementById('neutral-pct').textContent = neutralPct.toFixed(1) + '%';
                document.getElementById('neutral-bar').style.width = neutralPct + '%';

                document.getElementById('tweet-count').textContent = totalTweets;
                document.getElementById('sentiment-updated').textContent = 'Updated ' + new Date().toLocaleTimeString();
            } else {
                // No sentiment data available
                document.getElementById('overall-sentiment').textContent = 'No Data';
                document.getElementById('sentiment-score').textContent = 'N/A';
                document.getElementById('tweet-count').textContent = '0';
                document.getElementById('sentiment-updated').textContent = 'No data available';
            }
        }

        function updateTechnicalIndicators(indicators) {
            if (!indicators || indicators.error) {
                return;
            }

            // Update RSI
            const rsi = indicators.momentum?.rsi || 50;
            document.getElementById('rsi-value').textContent = rsi.toFixed(1);
            document.getElementById('rsi-bar').style.width = rsi + '%';

            // Color code RSI bar
            const rsiBar = document.getElementById('rsi-bar');
            if (rsi < 30) {
                rsiBar.className = 'bg-green-600 h-2 rounded-full'; // Oversold - potential buy
            } else if (rsi > 70) {
                rsiBar.className = 'bg-red-600 h-2 rounded-full'; // Overbought - potential sell
            } else {
                rsiBar.className = 'bg-blue-600 h-2 rounded-full'; // Neutral
            }

            // Update MACD
            const macd = indicators.momentum?.macd || 0;
            const macdSignal = indicators.momentum?.macd_signal || 0;
            document.getElementById('macd-value').textContent = macd.toFixed(2);

            const macdSignalElement = document.getElementById('macd-signal');
            if (macd > macdSignal) {
                macdSignalElement.textContent = 'Bullish';
                macdSignalElement.className = 'text-xs text-green-600';
            } else if (macd < macdSignal) {
                macdSignalElement.textContent = 'Bearish';
                macdSignalElement.className = 'text-xs text-red-600';
            } else {
                macdSignalElement.textContent = 'Neutral';
                macdSignalElement.className = 'text-xs text-gray-500';
            }

            // Update Bollinger Bands position
            const bbUpper = indicators.volatility?.bb_upper || 0;
            const bbLower = indicators.volatility?.bb_lower || 0;
            const currentPrice = indicators.price_indicators?.sma_20 || 0; // Approximate current price

            let bbPosition = 'Middle';
            if (currentPrice > bbUpper * 0.98) {
                bbPosition = 'Upper';
            } else if (currentPrice < bbLower * 1.02) {
                bbPosition = 'Lower';
            }
            document.getElementById('bb-position').textContent = bbPosition;

            // Update moving average signals
            const sma20Signal = indicators.signals?.price_above_sma20 ? '✅' : '❌';
            const sma50Signal = indicators.signals?.price_above_sma50 ? '✅' : '❌';

            document.getElementById('sma20-signal').textContent = sma20Signal;
            document.getElementById('sma50-signal').textContent = sma50Signal;
        }

        async function startTrading() {
            try {
                const response = await fetch('/api/trading/start', { method: 'POST' });
                const data = await response.json();
                
                if (response.ok) {
                    showNotification('Trading started successfully', 'success');
                } else {
                    showNotification(data.error || 'Failed to start trading', 'error');
                }
            } catch (error) {
                showNotification('Error starting trading', 'error');
            }
        }

        async function stopTrading() {
            try {
                const response = await fetch('/api/trading/stop', { method: 'POST' });
                const data = await response.json();
                
                if (response.ok) {
                    showNotification('Trading stopped successfully', 'success');
                } else {
                    showNotification(data.error || 'Failed to stop trading', 'error');
                }
            } catch (error) {
                showNotification('Error stopping trading', 'error');
            }
        }

        async function emergencyStop() {
            if (confirm('Are you sure you want to execute an emergency stop? This will immediately halt all trading activities.')) {
                try {
                    const response = await fetch('/api/emergency_stop', { method: 'POST' });
                    const data = await response.json();
                    
                    if (response.ok) {
                        showNotification('Emergency stop executed', 'warning');
                    } else {
                        showNotification(data.error || 'Failed to execute emergency stop', 'error');
                    }
                } catch (error) {
                    showNotification('Error executing emergency stop', 'error');
                }
            }
        }

        function showNotification(message, type) {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' :
                type === 'warning' ? 'bg-yellow-500 text-white' :
                'bg-red-500 text-white'
            }`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 3000);
        }
    </script>
</body>
</html>
