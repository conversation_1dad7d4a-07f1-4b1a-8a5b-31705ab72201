#!/usr/bin/env python3
"""
Web interface for the Crypto Markov Trader.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask, render_template, jsonify, request, redirect, url_for
import json
from datetime import datetime, timedelta
import pandas as pd
from loguru import logger

from crypto_markov_trader.trading import LiveTrader, TradingMonitor
from crypto_markov_trader.utils import setup_logging

app = Flask(__name__)
app.secret_key = 'crypto_markov_trader_secret_key'

# Global variables for trading system
live_trader = None
monitor = None
trading_active = False


def initialize_trading_system():
    """Initialize the trading system."""
    global live_trader, monitor
    
    try:
        if live_trader is None:
            live_trader = LiveTrader(
                symbols=['BTC', 'ETH', 'SOL'],
                initial_capital=10000
            )
            monitor = TradingMonitor(live_trader)
            logger.info("Trading system initialized for web interface")
        
        return True
    except Exception as e:
        logger.error(f"Error initializing trading system: {e}")
        return False


@app.route('/')
def dashboard():
    """Main dashboard."""
    return render_template('dashboard.html')


@app.route('/api/status')
def api_status():
    """Get current system status."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500

        status = live_trader.get_status()
        portfolio_summary = live_trader.portfolio_manager.get_portfolio_summary()
        risk_report = live_trader.risk_manager.get_risk_report()

        # Get real-time market data
        market_data = {}
        for symbol in live_trader.symbols:
            try:
                end_time = datetime.now()
                start_time = end_time - timedelta(minutes=5)

                data = live_trader.data_aggregator.hyperliquid_client.get_historical_data(
                    symbol=symbol,
                    interval="1m",
                    start_time=start_time,
                    end_time=end_time,
                    limit=5
                )

                if not data.empty:
                    latest = data.iloc[-1]
                    market_data[symbol] = {
                        'price': float(latest['close']),
                        'change_24h': 0.0,  # Calculate if historical data available
                        'volume': float(latest['volume']),
                        'timestamp': latest.name.isoformat()
                    }
            except Exception as e:
                logger.error(f"Error fetching market data for {symbol}: {e}")

        return jsonify({
            'timestamp': datetime.now().isoformat(),
            'trading_active': trading_active,
            'system_status': {
                'is_running': status['is_running'],
                'trading_enabled': status['trading_enabled'],
                'emergency_stop': status['emergency_stop'],
                'symbols': status['symbols']
            },
            'portfolio': {
                'total_value': status['portfolio_value'],
                'cash_balance': portfolio_summary['cash_balance'],
                'positions_value': portfolio_summary['positions_value'],
                'daily_pnl': status['daily_pnl'],
                'total_return': portfolio_summary['total_return'],
                'max_drawdown': portfolio_summary['max_drawdown'],
                'num_positions': status['num_positions']
            },
            'risk': {
                'risk_score': status['risk_score'],
                'emergency_stop': risk_report['emergency_stop'],
                'recent_alerts': len(risk_report.get('recent_alerts', []))
            },
            'trading_stats': status['trading_stats'],
            'market_data': market_data
        })

    except Exception as e:
        logger.error(f"Error getting status: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/sentiment')
def api_sentiment():
    """Get Twitter sentiment data."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500

        sentiment_data = {}

        for symbol in live_trader.symbols:
            try:
                # Get recent tweets
                tweets = live_trader.data_aggregator.twitter_client.search_tweets(
                    query=f"{symbol} OR ${symbol}",
                    max_results=50,
                    use_cache=True
                )

                if tweets:
                    # Analyze sentiment
                    sentiment_results = {}
                    total_sentiment = 0
                    positive_count = 0
                    negative_count = 0
                    neutral_count = 0

                    for tweet in tweets:
                        # Analyze individual tweet
                        result = live_trader.data_aggregator.sentiment_analyzer.analyze_text(tweet.get('text', ''))
                        if result:
                            sentiment_score = result.get('compound', 0)
                            total_sentiment += sentiment_score

                            if sentiment_score > 0.1:
                                positive_count += 1
                            elif sentiment_score < -0.1:
                                negative_count += 1
                            else:
                                neutral_count += 1

                    # Calculate ratios
                    total_tweets = len(tweets)
                    sentiment_results = {
                        'compound_sentiment': total_sentiment / total_tweets if total_tweets > 0 else 0,
                        'positive_ratio': positive_count / total_tweets if total_tweets > 0 else 0,
                        'negative_ratio': negative_count / total_tweets if total_tweets > 0 else 0,
                        'neutral_ratio': neutral_count / total_tweets if total_tweets > 0 else 0
                    }

                    sentiment_data[symbol] = {
                        'tweet_count': len(tweets),
                        'sentiment_score': sentiment_results.get('compound_sentiment', 0),
                        'positive_ratio': sentiment_results.get('positive_ratio', 0),
                        'negative_ratio': sentiment_results.get('negative_ratio', 0),
                        'neutral_ratio': sentiment_results.get('neutral_ratio', 0),
                        'last_updated': datetime.now().isoformat()
                    }
                else:
                    sentiment_data[symbol] = {
                        'tweet_count': 0,
                        'sentiment_score': 0,
                        'positive_ratio': 0,
                        'negative_ratio': 0,
                        'neutral_ratio': 1,
                        'last_updated': datetime.now().isoformat()
                    }

            except Exception as e:
                logger.error(f"Error getting sentiment for {symbol}: {e}")
                sentiment_data[symbol] = {
                    'tweet_count': 0,
                    'sentiment_score': 0,
                    'error': str(e)
                }

        return jsonify(sentiment_data)

    except Exception as e:
        logger.error(f"Error getting sentiment data: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/indicators/<symbol>')
def api_indicators(symbol):
    """Get technical indicators for a symbol."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500

        # Get recent market data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)

        market_data = live_trader.data_aggregator.hyperliquid_client.get_historical_data(
            symbol=symbol,
            interval="1h",
            start_time=start_time,
            end_time=end_time,
            limit=24
        )

        if market_data.empty:
            return jsonify({'error': 'No market data available'}), 404

        # Generate features/indicators
        features_df = live_trader.feature_pipeline.create_features(market_data, symbol)

        if features_df.empty:
            return jsonify({'error': 'No indicators generated'}), 500

        # Get latest indicators
        latest = features_df.iloc[-1]

        # Convert numpy types to Python native types for JSON serialization
        def convert_numpy_types(value):
            """Convert numpy types to Python native types."""
            import numpy as np
            if isinstance(value, (np.integer, np.int64, np.int32)):
                return int(value)
            elif isinstance(value, (np.floating, np.float64, np.float32)):
                return float(value)
            elif isinstance(value, np.bool_):
                return bool(value)
            return value

        indicators = {
            'timestamp': latest.name.isoformat(),
            'price_indicators': {
                'sma_20': convert_numpy_types(latest.get('sma_20', 0)),
                'sma_50': convert_numpy_types(latest.get('sma_50', 0)),
                'ema_12': convert_numpy_types(latest.get('ema_12', 0)),
                'ema_26': convert_numpy_types(latest.get('ema_26', 0)),
            },
            'momentum': {
                'rsi': convert_numpy_types(latest.get('rsi', 50)),
                'macd': convert_numpy_types(latest.get('macd', 0)),
                'macd_signal': convert_numpy_types(latest.get('macd_signal', 0)),
                'macd_histogram': convert_numpy_types(latest.get('macd_histogram', 0)),
            },
            'volatility': {
                'bb_upper': convert_numpy_types(latest.get('bb_upper', 0)),
                'bb_lower': convert_numpy_types(latest.get('bb_lower', 0)),
                'bb_width': convert_numpy_types(latest.get('bb_width', 0)),
                'atr': convert_numpy_types(latest.get('atr', 0)),
            },
            'volume': {
                'volume_sma': convert_numpy_types(latest.get('volume_sma', 0)),
                'volume_ratio': convert_numpy_types(latest.get('volume_ratio', 1)),
            },
            'signals': {
                'price_above_sma20': convert_numpy_types(latest.get('price_above_sma20', 0)),
                'price_above_sma50': convert_numpy_types(latest.get('price_above_sma50', 0)),
                'rsi_oversold': bool(convert_numpy_types(latest.get('rsi', 50)) < 30),
                'rsi_overbought': bool(convert_numpy_types(latest.get('rsi', 50)) > 70),
                'macd_bullish': convert_numpy_types(latest.get('macd_bullish', 0)),
            }
        }

        return jsonify(indicators)

    except Exception as e:
        logger.error(f"Error getting indicators for {symbol}: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/positions')
def api_positions():
    """Get current positions."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        positions = live_trader.portfolio_manager.get_all_positions()
        
        positions_data = []
        for symbol, position in positions.items():
            positions_data.append({
                'symbol': symbol,
                'quantity': position.quantity,
                'average_price': position.average_price,
                'market_value': position.market_value,
                'unrealized_pnl': position.unrealized_pnl,
                'unrealized_pnl_pct': (position.unrealized_pnl / abs(position.market_value)) * 100 if position.market_value != 0 else 0,
                'side': 'LONG' if position.quantity > 0 else 'SHORT'
            })
        
        return jsonify(positions_data)
        
    except Exception as e:
        logger.error(f"Error getting positions: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/performance')
def api_performance():
    """Get performance metrics."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        # Get performance history
        performance_history = []
        if hasattr(monitor, 'performance_history'):
            performance_history = monitor.get_performance_history(hours=24)
        
        # Get portfolio history
        portfolio_history = []
        if hasattr(live_trader.portfolio_manager, 'portfolio_history'):
            for snapshot in live_trader.portfolio_manager.portfolio_history[-100:]:  # Last 100 snapshots
                portfolio_history.append({
                    'timestamp': snapshot.timestamp.isoformat(),
                    'total_value': snapshot.total_value,
                    'realized_pnl': snapshot.realized_pnl,
                    'unrealized_pnl': snapshot.unrealized_pnl
                })
        
        return jsonify({
            'performance_history': performance_history,
            'portfolio_history': portfolio_history
        })
        
    except Exception as e:
        logger.error(f"Error getting performance: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/risk')
def api_risk():
    """Get risk metrics and alerts."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        risk_report = live_trader.risk_manager.get_risk_report()
        
        return jsonify({
            'risk_score': risk_report.get('risk_score', 0),
            'emergency_stop': risk_report.get('emergency_stop', False),
            'risk_metrics': risk_report.get('risk_metrics', {}),
            'risk_limits': risk_report.get('risk_limits', {}),
            'recent_alerts': [
                {
                    'timestamp': alert['timestamp'],
                    'level': alert['level'],
                    'category': alert['category'],
                    'message': alert['message'],
                    'action_required': alert.get('action_required', False)
                }
                for alert in risk_report.get('recent_alerts', [])
            ]
        })
        
    except Exception as e:
        logger.error(f"Error getting risk data: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/trading/start', methods=['POST'])
def api_start_trading():
    """Start trading."""
    global trading_active
    
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        if trading_active:
            return jsonify({'message': 'Trading is already active'})
        
        # Enable trading
        live_trader.enable_trading()
        trading_active = True
        
        logger.info("Trading started via web interface")
        
        return jsonify({'message': 'Trading started successfully'})
        
    except Exception as e:
        logger.error(f"Error starting trading: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/trading/stop', methods=['POST'])
def api_stop_trading():
    """Stop trading."""
    global trading_active
    
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        # Disable trading
        live_trader.disable_trading()
        trading_active = False
        
        logger.info("Trading stopped via web interface")
        
        return jsonify({'message': 'Trading stopped successfully'})
        
    except Exception as e:
        logger.error(f"Error stopping trading: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/emergency_stop', methods=['POST'])
def api_emergency_stop():
    """Emergency stop all trading."""
    global trading_active
    
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        # Execute emergency stop
        import asyncio
        asyncio.create_task(live_trader.emergency_stop())
        trading_active = False
        
        logger.critical("Emergency stop executed via web interface")
        
        return jsonify({'message': 'Emergency stop executed'})
        
    except Exception as e:
        logger.error(f"Error executing emergency stop: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/logs')
def api_logs():
    """Get recent log entries."""
    try:
        log_file = 'logs/trading.log'
        
        if not os.path.exists(log_file):
            return jsonify([])
        
        # Read last 100 lines
        with open(log_file, 'r') as f:
            lines = f.readlines()
        
        recent_lines = lines[-100:] if len(lines) > 100 else lines
        
        logs = []
        for line in recent_lines:
            if line.strip():
                logs.append({
                    'timestamp': datetime.now().isoformat(),  # Simplified
                    'message': line.strip()
                })
        
        return jsonify(logs)
        
    except Exception as e:
        logger.error(f"Error getting logs: {e}")
        return jsonify({'error': str(e)}), 500


@app.route('/api/market_data/<symbol>')
def api_market_data(symbol):
    """Get market data for symbol."""
    try:
        if not initialize_trading_system():
            return jsonify({'error': 'Failed to initialize trading system'}), 500
        
        # Get recent market data
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        market_data = live_trader.data_aggregator.hyperliquid_client.get_historical_data(
            symbol=symbol,
            interval="1h",
            start_time=start_time,
            end_time=end_time,
            limit=24
        )
        
        if market_data.empty:
            return jsonify([])
        
        # Convert to JSON format
        data = []
        for timestamp, row in market_data.iterrows():
            data.append({
                'timestamp': timestamp.isoformat(),
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close']),
                'volume': float(row['volume'])
            })
        
        return jsonify(data)
        
    except Exception as e:
        logger.error(f"Error getting market data for {symbol}: {e}")
        return jsonify({'error': str(e)}), 500


if __name__ == '__main__':
    setup_logging()
    
    # Create templates directory if it doesn't exist
    os.makedirs('web_interface/templates', exist_ok=True)
    os.makedirs('web_interface/static', exist_ok=True)
    
    logger.info("Starting Crypto Markov Trader Web Interface")
    app.run(host='0.0.0.0', port=5001, debug=True)
