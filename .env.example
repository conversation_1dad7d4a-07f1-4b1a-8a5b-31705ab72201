# Hyperliquid API Configuration
HYPERLIQUID_API_URL=https://api.hyperliquid.xyz
HYPERLIQUID_TESTNET=true

# Twitter/X API Configuration
TWITTER_BEARER_TOKEN=your_twitter_bearer_token_here
TWITTER_API_KEY=your_twitter_api_key_here
TWITTER_API_SECRET=your_twitter_api_secret_here
TWITTER_ACCESS_TOKEN=your_twitter_access_token_here
TWITTER_ACCESS_TOKEN_SECRET=your_twitter_access_token_secret_here

# MongoDB Configuration (for tweet storage)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=crypto_trading

# Trading Configuration
TRADING_SYMBOLS=BTC,ETH,SOL
DEFAULT_POSITION_SIZE=100
MAX_POSITION_SIZE=1000
RISK_PERCENTAGE=0.02

# Model Configuration
HMM_N_COMPONENTS=3
HMM_COVARIANCE_TYPE=full
SENTIMENT_MODEL=cardiffnlp/twitter-roberta-base-sentiment-latest

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/trading.log

# Backtesting Configuration
BACKTEST_START_DATE=2023-01-01
BACKTEST_END_DATE=2024-01-01
TRANSACTION_COST=0.001
