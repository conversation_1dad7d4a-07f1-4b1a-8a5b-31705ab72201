[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
pandas = "<2.1,>=1.5"
numpy = "<2.0"
requests = ">=2.31.0"
tweepy = ">=4.14.0"
pandas-ta = ">=0.3.14b"
hmmlearn = ">=0.3.0"
transformers = ">=4.30.0"
torch = ">=2.0.0"
datasets = ">=2.12.0"
pyarrow = ">=12.0.0"
pymongo = ">=4.4.0"
python-dotenv = ">=1.0.0"
pydantic = ">=2.0.0"
pydantic-settings = ">=2.0.0"
loguru = ">=0.7.0"
pytest = ">=7.4.0"
pytest-asyncio = ">=0.21.0"
black = ">=23.0.0"
isort = ">=5.12.0"
flake8 = ">=6.0.0"
docker = ">=6.1.0"
matplotlib = "*"
seaborn = "*"
cctx = "*"
websockets = "*"
aiohttp = "*"

[dev-packages]

[requires]
python_version = "3.11"
python_full_version = "3.11.13"
